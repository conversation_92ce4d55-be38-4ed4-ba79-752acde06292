'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { 
  ChevronDown,
  RefreshCw,
  Loader2,
  ArrowUp
} from 'lucide-react'

interface MobilePaginationProps<T> {
  items: T[]
  renderItem: (item: T, index: number) => React.ReactNode
  loadMore: () => Promise<T[]>
  hasMore: boolean
  loading?: boolean
  pageSize?: number
  enablePullToRefresh?: boolean
  onRefresh?: () => Promise<void>
  className?: string
}

export default function MobilePagination<T>({
  items,
  renderItem,
  loadMore,
  hasMore,
  loading = false,
  pageSize = 10,
  enablePullToRefresh = true,
  onRefresh,
  className = ''
}: MobilePaginationProps<T>) {
  const [displayItems, setDisplayItems] = useState<T[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [showScrollTop, setShowScrollTop] = useState(false)
  const [pullDistance, setPullDistance] = useState(0)
  const [isPulling, setIsPulling] = useState(false)
  
  const containerRef = useRef<HTMLDivElement>(null)
  const loadMoreRef = useRef<HTMLDivElement>(null)
  const touchStartY = useRef<number>(0)
  const scrollY = useRef<number>(0)

  // 初始化显示项目
  useEffect(() => {
    const startIndex = 0
    const endIndex = currentPage * pageSize
    setDisplayItems(items.slice(startIndex, endIndex))
  }, [items, currentPage, pageSize])

  // 监听滚动，显示回到顶部按钮
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      setShowScrollTop(scrollTop > 300)
      scrollY.current = scrollTop
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const handleLoadMore = useCallback(async () => {
    if (isLoadingMore || !hasMore) return

    setIsLoadingMore(true)
    try {
      const newItems = await loadMore()
      if (newItems.length > 0) {
        setCurrentPage(prev => prev + 1)
      }
    } catch (error) {
      console.error('加载更多失败:', error)
    } finally {
      setIsLoadingMore(false)
    }
  }, [isLoadingMore, hasMore, loadMore])

  // 无限滚动监听
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingMore && !loading) {
          handleLoadMore()
        }
      },
      { threshold: 0.1 }
    )

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current)
    }

    return () => observer.disconnect()
  }, [hasMore, isLoadingMore, loading, handleLoadMore])

  const handleRefresh = useCallback(async () => {
    if (!onRefresh || isRefreshing) return

    setIsRefreshing(true)
    try {
      await onRefresh()
      setCurrentPage(1)
      window.scrollTo({ top: 0, behavior: 'smooth' })
    } catch (error) {
      console.error('刷新失败:', error)
    } finally {
      setIsRefreshing(false)
      setPullDistance(0)
      setIsPulling(false)
    }
  }, [onRefresh, isRefreshing])

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  // 下拉刷新处理
  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (scrollY.current <= 0) {
      touchStartY.current = e.touches[0].clientY
    }
  }, [])

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!enablePullToRefresh || scrollY.current > 0) return

    const touchY = e.touches[0].clientY
    const distance = touchY - touchStartY.current

    if (distance > 0 && distance < 150) {
      setPullDistance(distance)
      setIsPulling(true)
      e.preventDefault()
    }
  }, [enablePullToRefresh])

  const handleTouchEnd = useCallback(() => {
    if (isPulling && pullDistance > 80) {
      handleRefresh()
    } else {
      setPullDistance(0)
      setIsPulling(false)
    }
  }, [isPulling, pullDistance, handleRefresh])

  useEffect(() => {
    const container = containerRef.current
    if (!container || !enablePullToRefresh) return

    container.addEventListener('touchstart', handleTouchStart, { passive: false })
    container.addEventListener('touchmove', handleTouchMove, { passive: false })
    container.addEventListener('touchend', handleTouchEnd)

    return () => {
      container.removeEventListener('touchstart', handleTouchStart)
      container.removeEventListener('touchmove', handleTouchMove)
      container.removeEventListener('touchend', handleTouchEnd)
    }
  }, [enablePullToRefresh, handleTouchStart, handleTouchMove, handleTouchEnd])

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* 下拉刷新指示器 */}
      {enablePullToRefresh && (
        <div 
          className={`absolute top-0 left-0 right-0 flex items-center justify-center transition-all duration-200 ${
            isPulling ? 'opacity-100' : 'opacity-0'
          }`}
          style={{ 
            transform: `translateY(${Math.min(pullDistance - 50, 0)}px)`,
            height: '50px'
          }}
        >
          <div className="flex items-center gap-2 text-blue-500">
            {isRefreshing ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">刷新中...</span>
              </>
            ) : pullDistance > 80 ? (
              <>
                <RefreshCw className="h-4 w-4" />
                <span className="text-sm">松开刷新</span>
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4" />
                <span className="text-sm">下拉刷新</span>
              </>
            )}
          </div>
        </div>
      )}

      {/* 内容区域 */}
      <div 
        className="transition-transform duration-200"
        style={{ 
          transform: isPulling ? `translateY(${Math.min(pullDistance, 100)}px)` : 'translateY(0)' 
        }}
      >
        {/* 列表项 */}
        <div className="space-y-4">
          {displayItems.map((item, index) => (
            <div key={index}>
              {renderItem(item, index)}
            </div>
          ))}
        </div>

        {/* 加载更多区域 */}
        {hasMore && (
          <div ref={loadMoreRef} className="py-8">
            {isLoadingMore ? (
              <div className="flex items-center justify-center">
                <Loader2 className="h-5 w-5 animate-spin text-blue-500 mr-2" />
                <span className="text-gray-600">加载中...</span>
              </div>
            ) : (
              <div className="text-center">
                <Button 
                  variant="outline" 
                  onClick={handleLoadMore}
                  disabled={loading}
                >
                  <ChevronDown className="mr-2 h-4 w-4" />
                  加载更多
                </Button>
              </div>
            )}
          </div>
        )}

        {/* 没有更多数据 */}
        {!hasMore && displayItems.length > 0 && (
          <div className="py-8 text-center text-gray-500">
            <div className="inline-flex items-center gap-2">
              <div className="w-12 h-px bg-gray-300"></div>
              <span className="text-sm">没有更多内容了</span>
              <div className="w-12 h-px bg-gray-300"></div>
            </div>
          </div>
        )}

        {/* 空状态 */}
        {displayItems.length === 0 && !loading && (
          <div className="py-16 text-center text-gray-500">
            <div className="text-4xl mb-4">📝</div>
            <p>暂无内容</p>
          </div>
        )}
      </div>

      {/* 回到顶部按钮 */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-20 right-4 z-50 w-12 h-12 bg-blue-500 text-white rounded-full shadow-lg hover:bg-blue-600 transition-all duration-200 flex items-center justify-center"
        >
          <ArrowUp className="h-5 w-5" />
        </button>
      )}

      {/* 分页信息 */}
      <div className="mt-6 text-center text-sm text-gray-500">
        <div className="inline-flex items-center gap-4">
          <span>已显示 {displayItems.length} 项</span>
          {hasMore && <span>• 还有更多内容</span>}
        </div>
      </div>
    </div>
  )
}
