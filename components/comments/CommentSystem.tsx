'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { 
  MessageCircle,
  Heart,
  Reply,
  MoreHorizontal,
  Send,
  User,
  Clock,
  ThumbsUp,
  Flag,
  Edit,
  Trash2
} from 'lucide-react'
import { mockApiResponse } from '@/lib/mock-data'

interface Comment {
  id: string
  content: string
  author: {
    id: string
    name: string
    avatar?: string
    level: 'member' | 'vip' | 'admin'
  }
  createdAt: string
  likes: number
  isLiked: boolean
  replies: Comment[]
  parentId?: string
}

interface CommentSystemProps {
  postId: string
  postType: 'article' | 'post' | 'inspiration'
  allowAnonymous?: boolean
}

export default function CommentSystem({ postId, postType, allowAnonymous = true }: CommentSystemProps) {
  const [comments, setComments] = useState<Comment[]>([])
  const [newComment, setNewComment] = useState('')
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyContent, setReplyContent] = useState('')
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [isAnonymous, setIsAnonymous] = useState(false)
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'popular'>('newest')
  const [editingComment, setEditingComment] = useState<string | null>(null)
  const [editContent, setEditContent] = useState('')
  const [showReportDialog, setShowReportDialog] = useState<string | null>(null)
  const [reportReason, setReportReason] = useState('')

  useEffect(() => {
    loadComments()
  }, [postId, sortBy])

  const loadComments = async () => {
    try {
      const mockComments: Comment[] = [
        {
          id: '1',
          content: '这篇文章写得真好，对我很有启发！特别是关于情绪管理的部分，我会尝试实践的。',
          author: {
            id: 'user1',
            name: '坚持的小明',
            level: 'vip'
          },
          createdAt: new Date(Date.now() - 3600000).toISOString(),
          likes: 12,
          isLiked: false,
          replies: [
            {
              id: '1-1',
              content: '同感！我也在学习情绪管理，一起加油！',
              author: {
                id: 'user2',
                name: '努力的小红',
                level: 'member'
              },
              createdAt: new Date(Date.now() - 1800000).toISOString(),
              likes: 3,
              isLiked: true,
              replies: [],
              parentId: '1'
            }
          ]
        },
        {
          id: '2',
          content: '感谢分享，这些方法我会试试看。希望能坚持下去！',
          author: {
            id: 'user3',
            name: '匿名用户',
            level: 'member'
          },
          createdAt: new Date(Date.now() - 7200000).toISOString(),
          likes: 8,
          isLiked: false,
          replies: []
        },
        {
          id: '3',
          content: '很实用的建议！已经收藏了，会经常回来看看。作者辛苦了！',
          author: {
            id: 'user4',
            name: '成长路上',
            level: 'admin'
          },
          createdAt: new Date(Date.now() - 10800000).toISOString(),
          likes: 15,
          isLiked: true,
          replies: []
        }
      ]

      const response = await mockApiResponse(mockComments)
      setComments(response.data)
    } catch (error) {
      console.error('加载评论失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmitComment = async () => {
    if (!newComment.trim()) return

    setSubmitting(true)
    try {
      const comment: Comment = {
        id: Date.now().toString(),
        content: newComment,
        author: {
          id: 'current-user',
          name: isAnonymous ? '匿名用户' : '当前用户',
          level: 'member'
        },
        createdAt: new Date().toISOString(),
        likes: 0,
        isLiked: false,
        replies: []
      }

      await mockApiResponse({ success: true })
      setComments(prev => [comment, ...prev])
      setNewComment('')
    } catch (error) {
      console.error('发布评论失败:', error)
    } finally {
      setSubmitting(false)
    }
  }

  const handleSubmitReply = async (parentId: string) => {
    if (!replyContent.trim()) return

    setSubmitting(true)
    try {
      const reply: Comment = {
        id: `${parentId}-${Date.now()}`,
        content: replyContent,
        author: {
          id: 'current-user',
          name: isAnonymous ? '匿名用户' : '当前用户',
          level: 'member'
        },
        createdAt: new Date().toISOString(),
        likes: 0,
        isLiked: false,
        replies: [],
        parentId
      }

      await mockApiResponse({ success: true })
      setComments(prev => prev.map(comment => 
        comment.id === parentId 
          ? { ...comment, replies: [...comment.replies, reply] }
          : comment
      ))
      setReplyContent('')
      setReplyingTo(null)
    } catch (error) {
      console.error('回复评论失败:', error)
    } finally {
      setSubmitting(false)
    }
  }

  const handleLikeComment = async (commentId: string, isReply: boolean = false, parentId?: string) => {
    try {
      await mockApiResponse({ success: true })
      
      if (isReply && parentId) {
        setComments(prev => prev.map(comment => 
          comment.id === parentId 
            ? {
                ...comment,
                replies: comment.replies.map(reply =>
                  reply.id === commentId
                    ? { 
                        ...reply, 
                        likes: reply.isLiked ? reply.likes - 1 : reply.likes + 1,
                        isLiked: !reply.isLiked 
                      }
                    : reply
                )
              }
            : comment
        ))
      } else {
        setComments(prev => prev.map(comment => 
          comment.id === commentId 
            ? { 
                ...comment, 
                likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1,
                isLiked: !comment.isLiked 
              }
            : comment
        ))
      }
    } catch (error) {
      console.error('点赞失败:', error)
    }
  }

  const handleEditComment = async (commentId: string) => {
    if (!editContent.trim()) return

    try {
      await mockApiResponse({ success: true })
      setComments(prev => prev.map(comment =>
        comment.id === commentId
          ? { ...comment, content: editContent.trim() }
          : comment
      ))
      setEditingComment(null)
      setEditContent('')
    } catch (error) {
      console.error('编辑评论失败:', error)
    }
  }

  const handleDeleteComment = async (commentId: string) => {
    if (!confirm('确定要删除这条评论吗？')) return

    try {
      await mockApiResponse({ success: true })
      setComments(prev => prev.filter(comment => comment.id !== commentId))
    } catch (error) {
      console.error('删除评论失败:', error)
    }
  }

  const handleReportComment = async (commentId: string) => {
    if (!reportReason.trim()) return

    try {
      await mockApiResponse({ success: true })
      console.log('举报评论:', commentId, '原因:', reportReason)
      setShowReportDialog(null)
      setReportReason('')
      alert('举报已提交，我们会尽快处理')
    } catch (error) {
      console.error('举报失败:', error)
    }
  }

  const startEditComment = (comment: Comment) => {
    setEditingComment(comment.id)
    setEditContent(comment.content)
  }

  const cancelEdit = () => {
    setEditingComment(null)
    setEditContent('')
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)
    
    if (minutes < 60) return `${minutes}分钟前`
    if (hours < 24) return `${hours}小时前`
    return `${days}天前`
  }

  const getLevelBadge = (level: string) => {
    switch (level) {
      case 'admin':
        return <Badge className="bg-red-100 text-red-800 text-xs">管理员</Badge>
      case 'vip':
        return <Badge className="bg-yellow-100 text-yellow-800 text-xs">VIP</Badge>
      default:
        return null
    }
  }

  const sortComments = (comments: Comment[]) => {
    switch (sortBy) {
      case 'oldest':
        return [...comments].sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
      case 'popular':
        return [...comments].sort((a, b) => b.likes - a.likes)
      default:
        return [...comments].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <MessageCircle className="mr-2 h-5 w-5" />
            评论 ({comments.length})
          </CardTitle>
          <div className="flex items-center gap-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="text-sm border rounded px-2 py-1"
            >
              <option value="newest">最新</option>
              <option value="oldest">最早</option>
              <option value="popular">最热</option>
            </select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* 发布评论 */}
          <div className="space-y-3">
            <div className="flex items-center gap-4">
              <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-gray-500" />
              </div>
              <div className="flex-1">
                <Input
                  placeholder="写下你的评论..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSubmitComment()}
                />
              </div>
            </div>
            <div className="flex items-center justify-between">
              {allowAnonymous && (
                <label className="flex items-center gap-2 text-sm text-gray-600">
                  <input
                    type="checkbox"
                    checked={isAnonymous}
                    onChange={(e) => setIsAnonymous(e.target.checked)}
                  />
                  匿名发布
                </label>
              )}
              <Button 
                onClick={handleSubmitComment}
                disabled={!newComment.trim() || submitting}
                size="sm"
              >
                <Send className="mr-2 h-4 w-4" />
                发布
              </Button>
            </div>
          </div>

          {/* 评论列表 */}
          <div className="space-y-4">
            {sortComments(comments).map((comment) => (
              <div key={comment.id} className="space-y-3">
                {/* 主评论 */}
                <div className="flex gap-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                    <User className="h-4 w-4 text-gray-500" />
                  </div>
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm">{comment.author.name}</span>
                      {getLevelBadge(comment.author.level)}
                      <span className="text-xs text-gray-500 flex items-center">
                        <Clock className="mr-1 h-3 w-3" />
                        {formatTime(comment.createdAt)}
                      </span>
                    </div>
                    {editingComment === comment.id ? (
                      <div className="space-y-2">
                        <Input
                          value={editContent}
                          onChange={(e) => setEditContent(e.target.value)}
                          placeholder="编辑评论..."
                        />
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => handleEditComment(comment.id)}
                            disabled={!editContent.trim()}
                          >
                            保存
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={cancelEdit}
                          >
                            取消
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <p className="text-gray-700 text-sm leading-relaxed">{comment.content}</p>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <button
                          onClick={() => handleLikeComment(comment.id)}
                          className={`flex items-center gap-1 text-xs ${
                            comment.isLiked ? 'text-red-500' : 'text-gray-500 hover:text-red-500'
                          } transition-colors`}
                        >
                          <Heart className={`h-3 w-3 ${comment.isLiked ? 'fill-current' : ''}`} />
                          {comment.likes}
                        </button>
                        <button
                          onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                          className="flex items-center gap-1 text-xs text-gray-500 hover:text-blue-500 transition-colors"
                        >
                          <Reply className="h-3 w-3" />
                          回复
                        </button>
                      </div>

                      {/* 更多操作 */}
                      <div className="flex items-center gap-2">
                        {comment.author.id === 'current-user' && editingComment !== comment.id && (
                          <>
                            <button
                              onClick={() => startEditComment(comment)}
                              className="flex items-center gap-1 text-xs text-gray-500 hover:text-blue-500 transition-colors"
                            >
                              <Edit className="h-3 w-3" />
                              编辑
                            </button>
                            <button
                              onClick={() => handleDeleteComment(comment.id)}
                              className="flex items-center gap-1 text-xs text-gray-500 hover:text-red-500 transition-colors"
                            >
                              <Trash2 className="h-3 w-3" />
                              删除
                            </button>
                          </>
                        )}
                        {comment.author.id !== 'current-user' && (
                          <button
                            onClick={() => setShowReportDialog(comment.id)}
                            className="flex items-center gap-1 text-xs text-gray-500 hover:text-yellow-500 transition-colors"
                          >
                            <Flag className="h-3 w-3" />
                            举报
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* 回复输入框 */}
                {replyingTo === comment.id && (
                  <div className="ml-11 space-y-2">
                    <Input
                      placeholder={`回复 ${comment.author.name}...`}
                      value={replyContent}
                      onChange={(e) => setReplyContent(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSubmitReply(comment.id)}
                    />
                    <div className="flex gap-2">
                      <Button 
                        size="sm" 
                        onClick={() => handleSubmitReply(comment.id)}
                        disabled={!replyContent.trim() || submitting}
                      >
                        回复
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => {
                          setReplyingTo(null)
                          setReplyContent('')
                        }}
                      >
                        取消
                      </Button>
                    </div>
                  </div>
                )}

                {/* 回复列表 */}
                {comment.replies.length > 0 && (
                  <div className="ml-11 space-y-3">
                    {comment.replies.map((reply) => (
                      <div key={reply.id} className="flex gap-3">
                        <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                          <User className="h-3 w-3 text-gray-500" />
                        </div>
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-xs">{reply.author.name}</span>
                            {getLevelBadge(reply.author.level)}
                            <span className="text-xs text-gray-500 flex items-center">
                              <Clock className="mr-1 h-2 w-2" />
                              {formatTime(reply.createdAt)}
                            </span>
                          </div>
                          <p className="text-gray-700 text-xs leading-relaxed">{reply.content}</p>
                          <div className="flex items-center gap-3">
                            <button
                              onClick={() => handleLikeComment(reply.id, true, comment.id)}
                              className={`flex items-center gap-1 text-xs ${
                                reply.isLiked ? 'text-red-500' : 'text-gray-500 hover:text-red-500'
                              } transition-colors`}
                            >
                              <Heart className={`h-2 w-2 ${reply.isLiked ? 'fill-current' : ''}`} />
                              {reply.likes}
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {comments.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <MessageCircle className="mx-auto h-12 w-12 text-gray-300 mb-4" />
              <p>还没有评论，来发表第一个评论吧！</p>
            </div>
          )}
        </div>
      </CardContent>

      {/* 举报对话框 */}
      {showReportDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">举报评论</h3>
              <button
                onClick={() => setShowReportDialog(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="reportReason">举报原因</Label>
                <select
                  id="reportReason"
                  value={reportReason}
                  onChange={(e) => setReportReason(e.target.value)}
                  className="w-full mt-1 px-3 py-2 border rounded-md"
                >
                  <option value="">请选择举报原因</option>
                  <option value="spam">垃圾信息</option>
                  <option value="inappropriate">不当内容</option>
                  <option value="harassment">骚扰他人</option>
                  <option value="false_info">虚假信息</option>
                  <option value="other">其他</option>
                </select>
              </div>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowReportDialog(null)
                    setReportReason('')
                  }}
                  className="flex-1"
                >
                  取消
                </Button>
                <Button
                  onClick={() => handleReportComment(showReportDialog)}
                  disabled={!reportReason}
                  className="flex-1"
                >
                  提交举报
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Card>
  )
}
