'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Heart,
  Bookmark,
  Share2,
  MessageCircle,
  Eye,
  ThumbsUp,
  Calendar,
  User,
  ArrowLeft,
  ExternalLink
} from 'lucide-react'
import { mockApiResponse } from '@/lib/mock-data'

// 生成模拟社交数据
function generateSocialDataList(type: 'likes' | 'bookmarks' | 'shares' | 'comments') {
  const baseData = {
    likes: [
      { id: '1', title: '戒色第30天的感悟分享', author: '坚持的小明', date: '2025-01-18', content: '今天是我戒色的第30天，感觉身体和精神状态都有了明显改善...', category: '经验分享' },
      { id: '2', title: '如何度过戒色初期的困难时光', author: '阳光少年', date: '2025-01-17', content: '戒色初期确实很困难，但是通过这些方法我成功度过了...', category: '方法技巧' },
      { id: '3', title: '戒色100天后的身心变化', author: '重生之路', date: '2025-01-16', content: '100天的坚持让我重新找回了自信和活力...', category: '成长记录' },
      { id: '4', title: '推荐几本对戒色有帮助的书籍', author: '书香青年', date: '2025-01-15', content: '这些书籍在我的戒色路上给了我很大帮助...', category: '资源推荐' },
      { id: '5', title: '戒色路上的心理调节技巧', author: '心理导师', date: '2025-01-14', content: '心理健康是戒色成功的关键因素...', category: '心理健康' }
    ],
    bookmarks: [
      { id: '1', title: '戒色必读：科学认识成瘾机制', author: '专业医师', date: '2025-01-18', content: '从医学角度解析成瘾的生理和心理机制...', category: '科学知识' },
      { id: '2', title: '冥想练习：每日10分钟的内心平静', author: '禅修导师', date: '2025-01-17', content: '通过冥想练习来提升自控力和内心平静...', category: '修行方法' },
      { id: '3', title: '运动健身：重塑身心的最佳方式', author: '健身教练', date: '2025-01-16', content: '规律的运动不仅强健体魄，更能提升意志力...', category: '健康生活' },
      { id: '4', title: '时间管理：充实生活远离诱惑', author: '效率专家', date: '2025-01-15', content: '合理安排时间，让生活充实有意义...', category: '生活技巧' }
    ],
    shares: [
      { id: '1', title: '戒色社区新人指南', author: '社区管理员', date: '2025-01-18', content: '欢迎新朋友加入我们的戒色大家庭...', category: '社区指南' },
      { id: '2', title: '戒色成功案例分享', author: '成功者联盟', date: '2025-01-17', content: '真实的成功案例给大家带来希望和动力...', category: '励志故事' },
      { id: '3', title: '戒色APP使用技巧大全', author: '技术达人', date: '2025-01-16', content: '充分利用APP的各项功能来辅助戒色...', category: '使用技巧' }
    ],
    comments: [
      { id: '1', title: '在"戒色第30天的感悟分享"中的评论', author: '坚持的小明', date: '2025-01-18', content: '谢谢大家的支持和鼓励，我会继续坚持下去的！', category: '回复评论' },
      { id: '2', title: '在"如何度过戒色初期的困难时光"中的评论', author: '阳光少年', date: '2025-01-17', content: '很实用的方法，特别是转移注意力的技巧很有效。', category: '经验交流' },
      { id: '3', title: '在"戒色100天后的身心变化"中的评论', author: '重生之路', date: '2025-01-16', content: '看到这么多朋友的进步，我也更有动力了！', category: '互相鼓励' }
    ]
  }

  return baseData[type] || []
}

interface SocialDataListProps {
  type: 'likes' | 'bookmarks' | 'shares' | 'comments'
  onBack: () => void
}

export default function SocialDataList({ type, onBack }: SocialDataListProps) {
  const [data, setData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  const typeConfig = {
    likes: { title: '我的点赞', icon: ThumbsUp, color: 'text-blue-500' },
    bookmarks: { title: '我的收藏', icon: Bookmark, color: 'text-yellow-500' },
    shares: { title: '我的分享', icon: Share2, color: 'text-green-500' },
    comments: { title: '我的评论', icon: MessageCircle, color: 'text-purple-500' }
  }

  const config = typeConfig[type]
  const IconComponent = config.icon

  useEffect(() => {
    loadData()
  }, [loadData])

  const loadData = useCallback(async () => {
    setLoading(true)
    try {
      const response = await mockApiResponse(generateSocialDataList(type))
      setData(response.data)
    } catch (error) {
      console.error('加载社交数据失败:', error)
    } finally {
      setLoading(false)
    }
  }, [type])

  const getCategoryColor = (category: string) => {
    const colors = {
      '经验分享': 'bg-blue-100 text-blue-800',
      '方法技巧': 'bg-green-100 text-green-800',
      '成长记录': 'bg-purple-100 text-purple-800',
      '资源推荐': 'bg-orange-100 text-orange-800',
      '心理健康': 'bg-pink-100 text-pink-800',
      '科学知识': 'bg-indigo-100 text-indigo-800',
      '修行方法': 'bg-teal-100 text-teal-800',
      '健康生活': 'bg-emerald-100 text-emerald-800',
      '生活技巧': 'bg-cyan-100 text-cyan-800',
      '社区指南': 'bg-gray-100 text-gray-800',
      '励志故事': 'bg-red-100 text-red-800',
      '使用技巧': 'bg-violet-100 text-violet-800',
      '回复评论': 'bg-amber-100 text-amber-800',
      '经验交流': 'bg-lime-100 text-lime-800',
      '互相鼓励': 'bg-rose-100 text-rose-800'
    }
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <div className="w-6 h-6 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        {[...Array(5)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="h-5 bg-gray-200 rounded w-48"></div>
                  <div className="h-5 bg-gray-200 rounded w-16"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-full"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                  </div>
                  <div className="h-8 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center space-x-3">
        <Button variant="ghost" size="icon" onClick={onBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <IconComponent className={`h-6 w-6 ${config.color}`} />
        <h2 className="text-xl font-semibold">{config.title}</h2>
        <Badge variant="secondary">{data.length} 项</Badge>
      </div>

      {/* 数据列表 */}
      <div className="space-y-4">
        {data.map((item) => (
          <Card key={item.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <h3 className="font-medium text-gray-900 flex-1 pr-4">{item.title}</h3>
                  <Badge className={getCategoryColor(item.category)}>
                    {item.category}
                  </Badge>
                </div>
                
                <p className="text-gray-600 text-sm line-clamp-2">{item.content}</p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <User className="h-4 w-4" />
                      <span>{item.author}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{item.date}</span>
                    </div>
                  </div>
                  
                  <Button variant="ghost" size="sm">
                    <ExternalLink className="h-4 w-4 mr-1" />
                    查看详情
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 空状态 */}
      {data.length === 0 && (
        <div className="text-center py-12">
          <IconComponent className={`h-12 w-12 ${config.color} mx-auto mb-4 opacity-50`} />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无{config.title.replace('我的', '')}记录</h3>
          <p className="text-gray-600">开始与社区互动，记录就会显示在这里</p>
        </div>
      )}
    </div>
  )
}
