'use client'

import { useEffect, useRef } from 'react'
import * as echarts from 'echarts'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { TrendingUp, Bar<PERSON>hart3, PieChart, Activity } from 'lucide-react'

interface ProgressChartsProps {
  isVip?: boolean
}

export default function ProgressCharts({ isVip = false }: ProgressChartsProps) {
  const moodChartRef = useRef<HTMLDivElement>(null)
  const progressChartRef = useRef<HTMLDivElement>(null)
  const weeklyChartRef = useRef<HTMLDivElement>(null)
  const comparisonChartRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // 情绪趋势图
    if (moodChartRef.current) {
      const moodChart = echarts.init(moodChartRef.current)
      const moodOption = {
        title: {
          text: '情绪趋势分析',
          textStyle: { fontSize: 16, fontWeight: 'normal' }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' }
        },
        legend: {
          data: ['情绪指数', '精力水平', '睡眠质量']
        },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 10
        },
        series: [
          {
            name: '情绪指数',
            type: 'line',
            smooth: true,
            data: [7, 8, 6, 9, 8, 7, 8],
            itemStyle: { color: '#3b82f6' }
          },
          {
            name: '精力水平',
            type: 'line',
            smooth: true,
            data: [6, 7, 8, 7, 9, 8, 7],
            itemStyle: { color: '#10b981' }
          },
          {
            name: '睡眠质量',
            type: 'line',
            smooth: true,
            data: [8, 7, 6, 8, 7, 9, 8],
            itemStyle: { color: '#f59e0b' }
          }
        ]
      }
      moodChart.setOption(moodOption)
    }

    // 进度完成度饼图
    if (progressChartRef.current) {
      const progressChart = echarts.init(progressChartRef.current)
      const progressOption = {
        title: {
          text: '任务完成度',
          textStyle: { fontSize: 16, fontWeight: 'normal' }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [
          {
            name: '任务完成度',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 85, name: '已完成', itemStyle: { color: '#10b981' } },
              { value: 10, name: '进行中', itemStyle: { color: '#f59e0b' } },
              { value: 5, name: '未开始', itemStyle: { color: '#ef4444' } }
            ]
          }
        ]
      }
      progressChart.setOption(progressOption)
    }

    // 周度对比柱状图
    if (weeklyChartRef.current) {
      const weeklyChart = echarts.init(weeklyChartRef.current)
      const weeklyOption = {
        title: {
          text: '周度表现对比',
          textStyle: { fontSize: 16, fontWeight: 'normal' }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        legend: {
          data: ['本周', '上周', '平均水平']
        },
        xAxis: {
          type: 'category',
          data: ['运动', '冥想', '学习', '社交', '睡眠']
        },
        yAxis: {
          type: 'value',
          max: 10
        },
        series: [
          {
            name: '本周',
            type: 'bar',
            data: [8, 7, 9, 6, 8],
            itemStyle: { color: '#3b82f6' }
          },
          {
            name: '上周',
            type: 'bar',
            data: [6, 8, 7, 7, 7],
            itemStyle: { color: '#94a3b8' }
          },
          {
            name: '平均水平',
            type: 'line',
            data: [7, 7.5, 8, 6.5, 7.5],
            itemStyle: { color: '#f59e0b' }
          }
        ]
      }
      weeklyChart.setOption(weeklyOption)
    }

    // VIP专享：月度对比分析
    if (comparisonChartRef.current && isVip) {
      const comparisonChart = echarts.init(comparisonChartRef.current)
      const comparisonOption = {
        title: {
          text: 'VIP专享：月度进步分析',
          textStyle: { fontSize: 16, fontWeight: 'normal' }
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['戒色天数', '情绪稳定性', '生活质量', '目标达成率']
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: [
          {
            type: 'value',
            name: '天数',
            position: 'left'
          },
          {
            type: 'value',
            name: '评分',
            position: 'right',
            max: 10
          }
        ],
        series: [
          {
            name: '戒色天数',
            type: 'bar',
            data: [15, 28, 45, 62, 78, 95],
            itemStyle: { color: '#10b981' }
          },
          {
            name: '情绪稳定性',
            type: 'line',
            yAxisIndex: 1,
            data: [6, 7, 7.5, 8, 8.5, 9],
            itemStyle: { color: '#3b82f6' }
          },
          {
            name: '生活质量',
            type: 'line',
            yAxisIndex: 1,
            data: [5.5, 6.5, 7, 7.8, 8.2, 8.8],
            itemStyle: { color: '#f59e0b' }
          },
          {
            name: '目标达成率',
            type: 'line',
            yAxisIndex: 1,
            data: [60, 70, 75, 82, 88, 92],
            itemStyle: { color: '#8b5cf6' }
          }
        ]
      }
      comparisonChart.setOption(comparisonOption)
    }

    // 清理函数
    return () => {
      const moodChart = moodChartRef.current
      const progressChart = progressChartRef.current
      const weeklyChart = weeklyChartRef.current
      const comparisonChart = comparisonChartRef.current

      if (moodChart) echarts.dispose(moodChart)
      if (progressChart) echarts.dispose(progressChart)
      if (weeklyChart) echarts.dispose(weeklyChart)
      if (isVip && comparisonChart) {
        echarts.dispose(comparisonChart)
      }
    }
  }, [isVip])

  return (
    <div className="space-y-6">
      {/* 基础图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="mr-2 h-5 w-5 text-blue-500" />
              情绪趋势分析
            </CardTitle>
            <CardDescription>
              过去一周的情绪、精力和睡眠质量变化
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div ref={moodChartRef} style={{ width: '100%', height: '300px' }} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="mr-2 h-5 w-5 text-green-500" />
              任务完成度
            </CardTitle>
            <CardDescription>
              本周任务完成情况统计
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div ref={progressChartRef} style={{ width: '100%', height: '300px' }} />
          </CardContent>
        </Card>
      </div>

      {/* 周度对比 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="mr-2 h-5 w-5 text-purple-500" />
            周度表现对比
          </CardTitle>
          <CardDescription>
            本周与上周各项指标对比分析
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div ref={weeklyChartRef} style={{ width: '100%', height: '400px' }} />
        </CardContent>
      </Card>

      {/* VIP专享图表 */}
      {isVip ? (
        <Card className="border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="mr-2 h-5 w-5 text-yellow-600" />
              VIP专享：月度进步分析
              <Badge className="ml-2 bg-yellow-100 text-yellow-800">VIP</Badge>
            </CardTitle>
            <CardDescription>
              深度分析你的长期进步趋势和改善方向
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div ref={comparisonChartRef} style={{ width: '100%', height: '400px' }} />
            <div className="mt-4 p-4 bg-white/80 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">AI分析结论：</h4>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• 你的情绪稳定性在过去6个月提升了50%，表现优秀</li>
                <li>• 生活质量持续改善，建议继续保持当前的作息规律</li>
                <li>• 目标达成率稳步提升，可以考虑设定更具挑战性的目标</li>
                <li>• 建议加强社交活动，这将进一步提升整体幸福感</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card className="border-gray-200 bg-gray-50">
          <CardContent className="p-8 text-center">
            <TrendingUp className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">升级VIP解锁高级分析</h3>
            <p className="text-gray-600 mb-4">
              获得月度进步分析、AI智能建议、个性化改善方案等专业功能
            </p>
            <button className="px-6 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors">
              立即升级VIP
            </button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
