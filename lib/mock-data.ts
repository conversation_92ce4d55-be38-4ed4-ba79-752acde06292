import { 
  User, 
  CheckInRecord, 
  RecoveryPlan, 
  Post, 
  Comment, 
  UserStats,
  DailyTask,
  Milestone 
} from '@/types'
import { format, subDays, addDays } from 'date-fns'

// 模拟用户数据
export const mockUser: User = {
  id: 'mock-user-123',
  email: '<EMAIL>',
  username: '坚持的小明',
  avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  created_at: '2024-01-01T00:00:00Z',
  profile: {
    age: 25,
    goal: 'quit_porn',
    history: 'tried_failed',
    triggers: ['深夜独处', '压力大', '无聊'],
    positive_energy_score: 85.6
  }
}

// 生成模拟打卡记录
export function generateMockCheckInRecords(days: number = 30): CheckInRecord[] {
  const records: CheckInRecord[] = []
  const today = new Date()
  
  for (let i = 0; i < days; i++) {
    const date = subDays(today, i)
    const isSuccess = Math.random() > 0.2 // 80% 成功率
    
    records.push({
      id: `checkin-${i}`,
      user_id: mockUser.id,
      check_in_date: format(date, 'yyyy-MM-dd'),
      status: isSuccess ? 'success' : 'relapse',
      mood_level: Math.floor(Math.random() * 5) + 1 as 1 | 2 | 3 | 4 | 5,
      notes: isSuccess ? '今天状态不错，继续加油！' : '遇到了一些挑战，明天会更好',
      challenges: isSuccess ? [] : ['压力大', '情绪低落'],
      created_at: date.toISOString()
    })
  }
  
  return records.reverse() // 按时间正序排列
}

// 模拟每日任务
export const mockDailyTasks: DailyTask[] = [
  {
    id: 'task-1',
    title: '晨间冥想',
    description: '进行10分钟的正念冥想，专注呼吸，平静内心',
    category: 'mindfulness',
    difficulty: 2,
    estimated_minutes: 10,
    completed: false,
    task_date: format(new Date(), 'yyyy-MM-dd')
  },
  {
    id: 'task-2',
    title: '户外运动',
    description: '进行30分钟的户外运动，如跑步、散步或骑行',
    category: 'physical',
    difficulty: 3,
    estimated_minutes: 30,
    completed: true,
    task_date: format(new Date(), 'yyyy-MM-dd')
  },
  {
    id: 'task-3',
    title: '阅读学习',
    description: '阅读一篇关于自我提升的文章或书籍章节',
    category: 'learning',
    difficulty: 2,
    estimated_minutes: 20,
    completed: false,
    task_date: format(new Date(), 'yyyy-MM-dd')
  }
]

// 模拟里程碑
export const mockMilestones: Milestone[] = [
  {
    id: 'milestone-1',
    title: '第一周',
    description: '成功坚持一周，建立初步习惯',
    target_days: 7,
    achieved: true,
    achieved_at: '2024-01-07T00:00:00Z'
  },
  {
    id: 'milestone-2',
    title: '第一个月',
    description: '坚持一个月，证明你的决心',
    target_days: 30,
    achieved: true,
    achieved_at: '2024-01-30T00:00:00Z'
  },
  {
    id: 'milestone-3',
    title: '三个月',
    description: '坚持三个月，习惯已经养成',
    target_days: 90,
    achieved: false
  }
]

// 模拟戒色计划
export const mockRecoveryPlan: RecoveryPlan = {
  id: 'plan-123',
  user_id: mockUser.id,
  status: 'active',
  plan_data: {
    duration_days: 90,
    daily_tasks: mockDailyTasks,
    milestones: mockMilestones,
    strategies: [
      {
        id: 'strategy-1',
        title: '深呼吸技巧',
        description: '当感到冲动时，进行4-7-8呼吸法',
        category: 'intervention',
        triggers: ['压力大', '情绪波动']
      },
      {
        id: 'strategy-2',
        title: '转移注意力',
        description: '立即离开当前环境，进行其他活动',
        category: 'prevention',
        triggers: ['深夜独处', '无聊']
      }
    ]
  },
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

// 虚拟用户名生成器
const generateAnonymousName = (): string => {
  const adjectives = [
    '坚强的', '勇敢的', '努力的', '坚持的', '奋斗的', '追梦的', '向上的', '积极的',
    '阳光的', '温暖的', '智慧的', '善良的', '真诚的', '专注的', '自律的', '进取的',
    '乐观的', '坚韧的', '果敢的', '淡定的', '从容的', '理性的', '冷静的', '沉稳的'
  ]
  const nouns = [
    '战士', '学者', '行者', '修行者', '探索者', '追求者', '实践者', '思考者',
    '守护者', '建设者', '创造者', '奋斗者', '坚持者', '努力者', '进步者', '成长者',
    '旅人', '求道者', '修炼者', '觉醒者', '蜕变者', '超越者', '突破者', '重生者'
  ]

  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)]
  const noun = nouns[Math.floor(Math.random() * nouns.length)]
  const number = String(Math.floor(Math.random() * 999) + 1).padStart(3, '0')

  return `${adjective}${noun}${number}`
}

// 虚拟帖子内容生成器
const generatePostContent = (type: 'share' | 'question' | 'support', filterTag?: string): { title: string; content: string; tags: string[] } => {
  const shareContents = [
    {
      title: '坚持30天的心得分享',
      content: '大家好，我已经坚持30天了！想分享一些心得：\n\n1. 建立晨间例行公事很重要，我每天早上都会冥想10分钟\n2. 运动真的有用，跑步能很好地释放压力\n3. 找到替代活动，比如阅读、画画等\n4. 社区的支持让我感到不孤单\n\n希望对大家有帮助，一起加油！💪',
      tags: ['经验分享', '30天', '冥想', '运动']
    },
    {
      title: '从破戒到重新站起来的90天',
      content: '三个月前我又一次破戒了，当时真的很沮丧。但是这次我决定换个方法：\n\n• 制定了详细的每日计划\n• 加入了线下的支持小组\n• 开始写日记记录情绪变化\n• 学会了正念冥想\n\n现在回头看，那次破戒反而成了转折点。失败不可怕，可怕的是不再尝试。',
      tags: ['重新开始', '90天', '破戒经历', '成长']
    },
    {
      title: '我的戒色工具箱分享',
      content: '分享一下我这一年来收集的有用工具：\n\n📱 App推荐：\n- 冥想类：Headspace、Calm\n- 习惯追踪：Habitica\n- 时间管理：Forest\n\n📚 书籍推荐：\n- 《原子习惯》\n- 《正念的奇迹》\n- 《意志力》\n\n🏃‍♂️ 运动：\n- 晨跑30分钟\n- 瑜伽拉伸\n- 力量训练\n\n希望对大家有帮助！',
      tags: ['工具分享', 'App推荐', '书籍', '运动']
    }
  ]

  const questionContents = [
    {
      title: '遇到挫折怎么办？',
      content: '最近遇到了一些挑战，连续几天都感觉很难坚持。工作压力大，晚上一个人的时候特别容易胡思乱想。\n\n想请教大家：\n1. 如何应对深夜的孤独感？\n2. 压力大的时候有什么好的疏解方法？\n3. 如何重新找回动力？\n\n谢谢大家的建议！🙏',
      tags: ['求助', '挫折', '压力', '孤独感']
    },
    {
      title: '新手求指导，该如何开始？',
      content: '大家好，我是新来的。之前尝试过很多次都失败了，这次想认真对待。\n\n我的情况：\n- 年龄25，程序员\n- 之前最长坚持过2周\n- 主要问题是深夜和周末\n\n想问问大家：\n1. 新手应该从哪里开始？\n2. 有什么必须避免的误区？\n3. 如何制定合理的目标？\n\n希望能得到大家的指导！',
      tags: ['新手', '求指导', '计划制定']
    },
    {
      title: '如何处理家人的不理解？',
      content: '最近在努力改变自己，但是家人不太理解我的做法，甚至觉得我小题大做。\n\n有时候想和他们分享进步，但得到的回应总是"这有什么好说的"。感觉很孤独，缺乏支持。\n\n请问大家是如何处理这种情况的？如何在缺乏家人支持的情况下坚持下去？',
      tags: ['家庭关系', '理解', '支持', '坚持']
    }
  ]

  const supportContents = [
    {
      title: '给正在挣扎的朋友们',
      content: '看到很多朋友在挣扎，想说几句鼓励的话：\n\n🌟 每一次重新开始都是勇气的体现\n🌟 进步不是直线，波动是正常的\n🌟 关注过程，而不只是结果\n🌟 你并不孤单，我们都在路上\n\n记住：失败不是终点，放弃才是。\n\n如果有需要倾诉的朋友，随时可以私信我。我们一起加油！💪❤️',
      tags: ['鼓励', '支持', '陪伴', '正能量']
    },
    {
      title: '今天是我的100天，想对大家说',
      content: '今天是我坚持的第100天！🎉\n\n想对还在路上的朋友们说：\n\n✨ 相信自己，你比想象中更强大\n✨ 每一天的坚持都有意义\n✨ 困难时记得寻求帮助\n✨ 庆祝每一个小进步\n\n这100天里，我学会了：\n- 接纳自己的不完美\n- 专注当下而不是焦虑未来\n- 从失败中学习而不是自责\n\n感谢这个社区给我的支持，让我们继续前行！',
      tags: ['100天', '里程碑', '感谢', '鼓励']
    }
  ]

  let contents: typeof shareContents
  switch (type) {
    case 'share':
      contents = shareContents
      break
    case 'question':
      contents = questionContents
      break
    case 'support':
      contents = supportContents
      break
  }

  // 如果指定了标签过滤，优先选择包含该标签的内容
  if (filterTag && filterTag !== 'all') {
    const filteredContents = contents.filter(content =>
      content.tags.some(tag => tag.includes(filterTag) || filterTag.includes(tag))
    )

    if (filteredContents.length > 0) {
      const selectedContent = filteredContents[Math.floor(Math.random() * filteredContents.length)]
      // 确保标签包含过滤标签
      return {
        ...selectedContent,
        tags: [filterTag, ...selectedContent.tags.filter(tag => tag !== filterTag)].slice(0, 4)
      }
    }
  }

  return contents[Math.floor(Math.random() * contents.length)]
}

// 生成虚拟社区帖子
export function generateMockPosts(count: number = 20, filterTag?: string): Post[] {
  const posts: Post[] = []
  const types: ('share' | 'question' | 'support')[] = ['share', 'question', 'support']

  for (let i = 0; i < count; i++) {
    const type = types[Math.floor(Math.random() * types.length)]
    const { title, content, tags } = generatePostContent(type, filterTag)
    const createdAt = subDays(new Date(), Math.floor(Math.random() * 30))

    // 如果指定了标签过滤，确保帖子包含该标签
    const finalTags = filterTag && filterTag !== 'all'
      ? [filterTag, ...tags.filter(tag => tag !== filterTag)].slice(0, 3)
      : tags

    posts.push({
      id: `post-${Date.now()}-${i + 1}`, // 使用时间戳确保唯一性
      user_id: `user-${i + 1}`,
      anonymous_id: generateAnonymousName(),
      title,
      content,
      type,
      tags: finalTags,
      likes_count: Math.floor(Math.random() * 50) + 1,
      comments_count: Math.floor(Math.random() * 20) + 1,
      is_moderated: false,
      created_at: createdAt.toISOString(),
      updated_at: createdAt.toISOString()
    })
  }

  return posts.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
}

// 默认帖子数据
export const mockPosts: Post[] = generateMockPosts(15)

// 模拟用户统计数据
export function generateMockUserStats(): UserStats {
  const checkInRecords = generateMockCheckInRecords(30)
  
  return {
    current_streak: 12,
    longest_streak: 25,
    total_days: 85,
    success_rate: 82,
    positive_energy_score: 85.6,
    mood_trend: checkInRecords.slice(-7).map(record => ({
      date: record.check_in_date,
      mood: record.mood_level
    })),
    weekly_progress: [
      { week: '第1周', success_days: 6, total_days: 7, success_rate: 86 },
      { week: '第2周', success_days: 7, total_days: 7, success_rate: 100 },
      { week: '第3周', success_days: 5, total_days: 7, success_rate: 71 },
      { week: '第4周', success_days: 6, total_days: 7, success_rate: 86 }
    ]
  }
}

// 生成虚拟排行榜数据
export function generateMockLeaderboard() {
  const streakLeaderboard = []
  const contributionLeaderboard = []

  // 连续天数排行榜
  for (let i = 1; i <= 20; i++) {
    streakLeaderboard.push({
      rank: i,
      name: generateAnonymousName(),
      days: Math.floor(Math.random() * 200) + 10,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${i}`,
      level: Math.floor(Math.random() * 10) + 1
    })
  }

  // 社区贡献排行榜
  for (let i = 1; i <= 20; i++) {
    const posts = Math.floor(Math.random() * 30) + 1
    const likes = Math.floor(Math.random() * 200) + 10
    contributionLeaderboard.push({
      rank: i,
      name: generateAnonymousName(),
      points: posts * 50 + likes * 5,
      posts,
      likes,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${i + 100}`,
      level: Math.floor(Math.random() * 8) + 1
    })
  }

  // 按分数排序
  streakLeaderboard.sort((a, b) => b.days - a.days)
  contributionLeaderboard.sort((a, b) => b.points - a.points)

  // 更新排名
  streakLeaderboard.forEach((user, index) => user.rank = index + 1)
  contributionLeaderboard.forEach((user, index) => user.rank = index + 1)

  return {
    streakLeaderboard,
    contributionLeaderboard
  }
}

// 生成虚拟成就数据
export function generateMockAchievements() {
  const allAchievements = [
    {
      id: 'first_week',
      name: '初心不改',
      description: '连续戒色7天',
      icon: 'Shield',
      color: 'bg-blue-500',
      category: 'streak',
      requirement: 7,
      rarity: 'common'
    },
    {
      id: 'first_month',
      name: '坚持不懈',
      description: '连续戒色30天',
      icon: 'Target',
      color: 'bg-green-500',
      category: 'streak',
      requirement: 30,
      rarity: 'uncommon'
    },
    {
      id: 'three_months',
      name: '百日筑基',
      description: '连续戒色100天',
      icon: 'Crown',
      color: 'bg-purple-500',
      category: 'streak',
      requirement: 100,
      rarity: 'rare'
    },
    {
      id: 'half_year',
      name: '半年修行',
      description: '连续戒色180天',
      icon: 'Award',
      color: 'bg-indigo-500',
      category: 'streak',
      requirement: 180,
      rarity: 'epic'
    },
    {
      id: 'one_year',
      name: '年度大师',
      description: '连续戒色365天',
      icon: 'Trophy',
      color: 'bg-yellow-500',
      category: 'streak',
      requirement: 365,
      rarity: 'legendary'
    },
    {
      id: 'community_star',
      name: '社区之星',
      description: '获得100个点赞',
      icon: 'Star',
      color: 'bg-yellow-500',
      category: 'social',
      requirement: 100,
      rarity: 'uncommon'
    },
    {
      id: 'helper',
      name: '助人为乐',
      description: '帮助10位戒友',
      icon: 'Heart',
      color: 'bg-red-500',
      category: 'social',
      requirement: 10,
      rarity: 'common'
    },
    {
      id: 'knowledge_sharer',
      name: '知识分享者',
      description: '发布5篇优质文章',
      icon: 'BookOpen',
      color: 'bg-indigo-500',
      category: 'content',
      requirement: 5,
      rarity: 'uncommon'
    },
    {
      id: 'mentor',
      name: '社区导师',
      description: '指导50位新人',
      icon: 'Users',
      color: 'bg-purple-500',
      category: 'social',
      requirement: 50,
      rarity: 'rare'
    },
    {
      id: 'daily_checker',
      name: '每日打卡王',
      description: '连续打卡30天',
      icon: 'Calendar',
      color: 'bg-green-500',
      category: 'habit',
      requirement: 30,
      rarity: 'common'
    }
  ]

  // 模拟用户已获得的成就
  const userAchievements = allAchievements.map(achievement => ({
    ...achievement,
    achieved: Math.random() > 0.6, // 40% 获得率
    achievedAt: Math.random() > 0.6 ? subDays(new Date(), Math.floor(Math.random() * 100)).toISOString() : undefined,
    progress: Math.floor(Math.random() * achievement.requirement)
  }))

  return {
    allAchievements,
    userAchievements
  }
}

// 生成虚拟社区统计数据
export function generateMockCommunityStats() {
  const today = new Date()
  const stats = {
    totalUsers: Math.floor(Math.random() * 5000) + 1000,
    activeUsers: Math.floor(Math.random() * 1000) + 200,
    todayPosts: Math.floor(Math.random() * 50) + 10,
    todayComments: Math.floor(Math.random() * 200) + 50,
    totalInteractions: Math.floor(Math.random() * 10000) + 2000,
    weeklyGrowth: Math.floor(Math.random() * 100) + 10,

    // 每日活跃度数据（最近7天）
    dailyActivity: Array.from({ length: 7 }, (_, i) => ({
      date: format(subDays(today, 6 - i), 'MM/dd'),
      posts: Math.floor(Math.random() * 30) + 10,
      comments: Math.floor(Math.random() * 100) + 20,
      likes: Math.floor(Math.random() * 200) + 50,
      activeUsers: Math.floor(Math.random() * 300) + 100
    })),

    // 热门标签
    popularTags: [
      { tag: '经验分享', count: Math.floor(Math.random() * 100) + 50 },
      { tag: '新手求助', count: Math.floor(Math.random() * 80) + 30 },
      { tag: '30天', count: Math.floor(Math.random() * 60) + 25 },
      { tag: '冥想', count: Math.floor(Math.random() * 50) + 20 },
      { tag: '运动', count: Math.floor(Math.random() * 45) + 18 },
      { tag: '正能量', count: Math.floor(Math.random() * 40) + 15 },
      { tag: '破戒经历', count: Math.floor(Math.random() * 35) + 12 },
      { tag: '工具推荐', count: Math.floor(Math.random() * 30) + 10 }
    ].sort((a, b) => b.count - a.count)
  }

  return stats
}

// 生成虚拟用户活动数据
export function generateMockUserActivity(days: number = 30) {
  const activities = []
  const activityTypes = [
    { type: 'check_in', label: '每日打卡', icon: 'Calendar' },
    { type: 'post_created', label: '发布帖子', icon: 'MessageSquare' },
    { type: 'comment_added', label: '评论回复', icon: 'MessageCircle' },
    { type: 'like_given', label: '点赞支持', icon: 'Heart' },
    { type: 'achievement_earned', label: '获得成就', icon: 'Award' },
    { type: 'milestone_reached', label: '达成里程碑', icon: 'Target' },
    { type: 'task_completed', label: '完成任务', icon: 'CheckCircle' }
  ]

  for (let i = 0; i < days; i++) {
    const date = subDays(new Date(), i)
    const dailyActivities = Math.floor(Math.random() * 5) + 1

    for (let j = 0; j < dailyActivities; j++) {
      const activity = activityTypes[Math.floor(Math.random() * activityTypes.length)]
      activities.push({
        id: `activity-${i}-${j}`,
        type: activity.type,
        label: activity.label,
        icon: activity.icon,
        description: `在 ${format(date, 'MM月dd日')} ${activity.label}`,
        timestamp: date.toISOString(),
        points: Math.floor(Math.random() * 20) + 5
      })
    }
  }

  return activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
}

// 生成虚拟正气值历史数据
export function generateMockPositiveEnergyHistory(days: number = 90) {
  const history = []
  let currentValue = 80 // 初始正气值
  const today = new Date()

  for (let i = days - 1; i >= 0; i--) {
    const date = subDays(today, i)

    // 模拟正气值的自然波动
    const dailyChange = (Math.random() - 0.5) * 10 // -5 到 +5 的随机变化
    const weeklyTrend = Math.sin((i / 7) * Math.PI) * 3 // 周期性波动
    const monthlyTrend = (days - i) / days * 15 // 长期上升趋势

    currentValue += dailyChange + weeklyTrend * 0.1 + monthlyTrend * 0.05
    currentValue = Math.max(0, Math.min(100, currentValue)) // 限制在0-100之间

    history.push({
      date: format(date, 'yyyy-MM-dd'),
      value: Math.round(currentValue * 10) / 10,
      mood: Math.floor(Math.random() * 5) + 1,
      energy: Math.floor(Math.random() * 5) + 1,
      stress: Math.floor(Math.random() * 5) + 1
    })
  }

  return history
}

// 生成虚拟破戒原因分析数据
export function generateMockRelapseAnalysis() {
  const reasons = [
    { reason: '压力过大', weight: 0.25 },
    { reason: '情绪低落', weight: 0.20 },
    { reason: '无聊空虚', weight: 0.15 },
    { reason: '社交媒体', weight: 0.12 },
    { reason: '深夜独处', weight: 0.10 },
    { reason: '工作疲劳', weight: 0.08 },
    { reason: '人际关系', weight: 0.06 },
    { reason: '其他原因', weight: 0.04 }
  ]

  const totalCount = Math.floor(Math.random() * 50) + 20

  return reasons.map(item => ({
    reason: item.reason,
    count: Math.floor(totalCount * item.weight),
    percentage: Math.round(item.weight * 100),
    color: `hsl(${Math.random() * 360}, 70%, 50%)`
  }))
}

// 生成虚拟挑战赛数据
export function generateMockChallenges() {
  const challengeTypes = [
    { name: '7天新手挑战', duration: 7, reward: 50, difficulty: 1 },
    { name: '30天进阶挑战', duration: 30, reward: 200, difficulty: 2 },
    { name: '90天大师挑战', duration: 90, reward: 500, difficulty: 3 },
    { name: '365天传奇挑战', duration: 365, reward: 2000, difficulty: 5 }
  ]

  return challengeTypes.map((challenge, index) => ({
    id: `challenge-${index + 1}`,
    name: challenge.name,
    description: `坚持${challenge.duration}天戒色，获得${challenge.reward}挑战金奖励`,
    duration: challenge.duration,
    reward: challenge.reward,
    difficulty: challenge.difficulty,
    participants: Math.floor(Math.random() * 1000) + 100,
    completionRate: Math.floor(Math.random() * 30) + 10,
    isActive: Math.random() > 0.3,
    startDate: format(addDays(new Date(), Math.floor(Math.random() * 30)), 'yyyy-MM-dd'),
    endDate: format(addDays(new Date(), challenge.duration + Math.floor(Math.random() * 30)), 'yyyy-MM-dd')
  }))
}

// 生成虚拟修真等级数据
export function generateMockCultivationLevels() {
  const levels = [
    { name: '练气期', minDays: 0, maxDays: 6, color: 'bg-gray-500' },
    { name: '筑基期', minDays: 7, maxDays: 29, color: 'bg-green-500' },
    { name: '结丹期', minDays: 30, maxDays: 89, color: 'bg-blue-500' },
    { name: '元婴期', minDays: 90, maxDays: 179, color: 'bg-purple-500' },
    { name: '化神期', minDays: 180, maxDays: 364, color: 'bg-indigo-500' },
    { name: '合体期', minDays: 365, maxDays: 729, color: 'bg-yellow-500' },
    { name: '大乘期', minDays: 730, maxDays: 1094, color: 'bg-orange-500' },
    { name: '渡劫期', minDays: 1095, maxDays: 1824, color: 'bg-red-500' },
    { name: '飞升期', minDays: 1825, maxDays: Infinity, color: 'bg-gradient-to-r from-purple-500 to-pink-500' }
  ]

  const currentDays = Math.floor(Math.random() * 200) + 1
  const currentLevel = levels.find(level => currentDays >= level.minDays && currentDays <= level.maxDays) || levels[0]
  const nextLevel = levels[levels.indexOf(currentLevel) + 1]

  return {
    currentLevel,
    nextLevel,
    currentDays,
    progress: nextLevel ? ((currentDays - currentLevel.minDays) / (nextLevel.minDays - currentLevel.minDays)) * 100 : 100,
    allLevels: levels
  }
}

// 生成虚拟专家问答数据
export function generateMockExpertQA(count: number = 10) {
  const experts = [
    { name: '李心理咨询师', title: '国家二级心理咨询师', specialty: '心理健康' },
    { name: '王行为治疗师', title: '认知行为治疗专家', specialty: '行为矫正' },
    { name: '张医学博士', title: '泌尿外科主任医师', specialty: '生理健康' },
    { name: '陈正念导师', title: '正念冥想认证导师', specialty: '正念修行' },
    { name: '刘营养师', title: '注册营养师', specialty: '健康饮食' }
  ]

  const questionTemplates = [
    '戒色过程中出现{symptom}怎么办？',
    '如何应对{situation}时的冲动？',
    '{problem}对身体有什么影响？',
    '戒色{duration}后为什么会{feeling}？',
    '如何建立健康的{habit}？'
  ]

  const symptoms = ['焦虑情绪', '失眠', '注意力不集中', '情绪波动', '身体不适']
  const situations = ['深夜独处', '压力大', '情绪低落', '无聊', '看到诱惑内容']
  const problems = ['长期手淫', '色情成瘾', '性功能障碍', '前列腺问题', '精神萎靡']
  const durations = ['一周', '一个月', '三个月', '半年', '一年']
  const feelings = ['疲劳', '兴奋', '抑郁', '焦虑', '充满活力']
  const habits = ['作息规律', '运动习惯', '饮食习惯', '学习习惯', '社交习惯']

  const qaList = []

  for (let i = 0; i < count; i++) {
    const expert = experts[Math.floor(Math.random() * experts.length)]
    const template = questionTemplates[Math.floor(Math.random() * questionTemplates.length)]

    let question = template
    question = question.replace('{symptom}', symptoms[Math.floor(Math.random() * symptoms.length)])
    question = question.replace('{situation}', situations[Math.floor(Math.random() * situations.length)])
    question = question.replace('{problem}', problems[Math.floor(Math.random() * problems.length)])
    question = question.replace('{duration}', durations[Math.floor(Math.random() * durations.length)])
    question = question.replace('{feeling}', feelings[Math.floor(Math.random() * feelings.length)])
    question = question.replace('{habit}', habits[Math.floor(Math.random() * habits.length)])

    const createdAt = subDays(new Date(), Math.floor(Math.random() * 30))

    qaList.push({
      id: `qa-${i + 1}`,
      question,
      answer: `这是一个很好的问题。根据我的专业经验，建议您采取以下几个步骤：\n\n1. 首先要理解这是正常的生理和心理反应\n2. 建立规律的作息和运动习惯\n3. 寻求专业的心理支持\n4. 保持积极的心态和耐心\n\n如果问题持续存在，建议寻求专业医疗帮助。`,
      expert: expert.name,
      expertTitle: expert.title,
      specialty: expert.specialty,
      likes: Math.floor(Math.random() * 100) + 10,
      views: Math.floor(Math.random() * 1000) + 100,
      createdAt: createdAt.toISOString(),
      isAnswered: Math.random() > 0.2
    })
  }

  return qaList.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
}

// 生成虚拟推荐内容数据
export function generateMockRecommendationContent() {
  const articles = [
    {
      id: 'article-1',
      type: 'article' as const,
      title: '戒色的科学原理：大脑神经可塑性研究',
      description: '深入了解戒色过程中大脑的变化机制，基于最新神经科学研究',
      category: '科学知识',
      tags: ['神经科学', '大脑健康', '科学研究'],
      difficulty_level: 4 as const,
      estimated_duration: 15,
      target_audience: ['中级用户', '高级用户'],
      effectiveness_score: 92,
      popularity_score: 85,
      created_at: subDays(new Date(), 5).toISOString(),
      updated_at: subDays(new Date(), 2).toISOString(),
      metadata: {
        author: '神经科学研究院',
        source: '学术期刊',
        language: 'zh-CN',
        format: 'long-form'
      }
    },
    {
      id: 'article-2',
      type: 'article' as const,
      title: '新手指南：戒色第一周的注意事项',
      description: '为戒色新手提供实用的第一周指导，包含常见问题和解决方案',
      category: '新手指导',
      tags: ['新手', '第一周', '实用技巧'],
      difficulty_level: 1 as const,
      estimated_duration: 8,
      target_audience: ['新手'],
      effectiveness_score: 88,
      popularity_score: 95,
      created_at: subDays(new Date(), 10).toISOString(),
      updated_at: subDays(new Date(), 1).toISOString(),
      metadata: {
        author: '戒色导师团队',
        source: '官方指南',
        language: 'zh-CN',
        format: 'guide'
      }
    },
    {
      id: 'exercise-1',
      type: 'exercise' as const,
      title: '5分钟正念冥想练习',
      description: '简单易学的正念冥想练习，帮助缓解压力和焦虑情绪',
      category: '冥想练习',
      tags: ['正念', '冥想', '压力缓解'],
      difficulty_level: 2 as const,
      estimated_duration: 5,
      target_audience: ['所有用户'],
      effectiveness_score: 90,
      popularity_score: 88,
      created_at: subDays(new Date(), 3).toISOString(),
      updated_at: subDays(new Date(), 1).toISOString(),
      metadata: {
        author: '正念导师',
        source: '冥想中心',
        language: 'zh-CN',
        format: 'guided-audio'
      }
    },
    {
      id: 'challenge-1',
      type: 'challenge' as const,
      title: '21天早起挑战',
      description: '通过21天的早起习惯养成，重塑生活节奏，提升自控力',
      category: '习惯养成',
      tags: ['早起', '习惯', '自控力'],
      difficulty_level: 3 as const,
      estimated_duration: 30,
      target_audience: ['中级用户'],
      effectiveness_score: 85,
      popularity_score: 78,
      created_at: subDays(new Date(), 7).toISOString(),
      updated_at: subDays(new Date(), 3).toISOString(),
      metadata: {
        author: '习惯教练',
        source: '挑战平台',
        language: 'zh-CN',
        format: 'interactive'
      }
    },
    {
      id: 'video-1',
      type: 'video' as const,
      title: '专家访谈：如何应对戒色过程中的情绪波动',
      description: '心理学专家深度解析戒色过程中的情绪管理策略',
      category: '专家访谈',
      tags: ['情绪管理', '专家访谈', '心理学'],
      difficulty_level: 3 as const,
      estimated_duration: 25,
      target_audience: ['中级用户', '高级用户'],
      effectiveness_score: 93,
      popularity_score: 82,
      created_at: subDays(new Date(), 12).toISOString(),
      updated_at: subDays(new Date(), 4).toISOString(),
      metadata: {
        author: '心理学专家',
        source: '专业访谈',
        language: 'zh-CN',
        format: 'video'
      }
    }
  ]

  return articles
}

// 生成虚拟用户行为数据
export function generateMockUserBehaviors(userId: string, days: number = 30) {
  const behaviors = []
  const contentIds = ['article-1', 'article-2', 'exercise-1', 'challenge-1', 'video-1']
  const actionTypes = ['view', 'like', 'comment', 'share', 'complete', 'skip'] as const
  const contentTypes = ['article', 'video', 'audio', 'exercise', 'challenge'] as const
  const categories = ['科学知识', '新手指导', '冥想练习', '习惯养成', '专家访谈']

  for (let i = 0; i < days * 3; i++) {
    const date = subDays(new Date(), Math.floor(Math.random() * days))
    const contentId = contentIds[Math.floor(Math.random() * contentIds.length)]
    const actionType = actionTypes[Math.floor(Math.random() * actionTypes.length)]
    const contentType = contentTypes[Math.floor(Math.random() * contentTypes.length)]
    const category = categories[Math.floor(Math.random() * categories.length)]

    behaviors.push({
      id: `behavior-${i}`,
      user_id: userId,
      action_type: actionType,
      content_type: contentType,
      content_id: contentId,
      content_category: category,
      content_tags: ['标签1', '标签2', '标签3'],
      duration: Math.floor(Math.random() * 600) + 30, // 30秒到10分钟
      rating: Math.floor(Math.random() * 5) + 1,
      timestamp: date.toISOString(),
      context: {
        device_type: Math.random() > 0.5 ? 'mobile' : 'desktop',
        time_of_day: date.getHours() < 12 ? 'morning' : date.getHours() < 18 ? 'afternoon' : 'evening',
        mood_before: Math.floor(Math.random() * 5) + 1,
        mood_after: Math.floor(Math.random() * 5) + 1
      }
    })
  }

  return behaviors.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
}

// 生成虚拟个性化推荐
export function generateMockPersonalizedRecommendations(userId: string) {
  const recommendations = [
    {
      content_id: 'article-1',
      score: 0.92,
      reasons: ['基于您对科学知识的兴趣', '符合您的阅读习惯', '其他用户高度评价'],
      category: '科学知识',
      priority: 'high' as const,
      timing_suggestion: '建议在晚上8-10点阅读',
      personalization_factors: {
        behavior_match: 0.88,
        preference_match: 0.95,
        context_match: 0.85,
        novelty_score: 0.75
      }
    },
    {
      content_id: 'exercise-1',
      score: 0.87,
      reasons: ['适合您的当前情绪状态', '与您的冥想习惯匹配', '时长符合您的偏好'],
      category: '冥想练习',
      priority: 'high' as const,
      timing_suggestion: '建议在早晨或睡前进行',
      personalization_factors: {
        behavior_match: 0.82,
        preference_match: 0.90,
        context_match: 0.88,
        novelty_score: 0.65
      }
    },
    {
      content_id: 'challenge-1',
      score: 0.78,
      reasons: ['挑战难度适中', '与您的目标一致', '社区参与度高'],
      category: '习惯养成',
      priority: 'medium' as const,
      timing_suggestion: '建议在周一开始',
      personalization_factors: {
        behavior_match: 0.75,
        preference_match: 0.80,
        context_match: 0.70,
        novelty_score: 0.85
      }
    },
    {
      content_id: 'video-1',
      score: 0.73,
      reasons: ['专家内容质量高', '时长适合您的习惯', '话题相关性强'],
      category: '专家访谈',
      priority: 'medium' as const,
      timing_suggestion: '建议在空闲时间观看',
      personalization_factors: {
        behavior_match: 0.70,
        preference_match: 0.75,
        context_match: 0.72,
        novelty_score: 0.80
      }
    },
    {
      content_id: 'article-2',
      score: 0.65,
      reasons: ['基础内容回顾', '巩固知识基础', '用户反馈良好'],
      category: '新手指导',
      priority: 'low' as const,
      timing_suggestion: '建议在需要时查阅',
      personalization_factors: {
        behavior_match: 0.60,
        preference_match: 0.68,
        context_match: 0.65,
        novelty_score: 0.45
      }
    }
  ]

  return recommendations
}

// 生成虚拟正能量壁纸数据
export function generateMockInspirationWallpapers() {
  const wallpapers = [
    {
      id: 'wallpaper-1',
      title: '山峰日出 - 新的开始',
      description: '壮丽的山峰日出，象征着每一天都是新的开始，充满无限可能',
      category: '自然风光',
      theme: '励志',
      resolution: '4K',
      size: '3.2MB',
      downloads: 15420,
      likes: 2847,
      tags: ['日出', '山峰', '新开始', '希望'],
      preview_url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',
      download_urls: {
        '1080p': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop',
        '2K': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=2560&h=1440&fit=crop',
        '4K': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=3840&h=2160&fit=crop'
      },
      quote: '每一次日出都是新的机会',
      color_palette: ['#FF6B35', '#F7931E', '#FFD23F', '#06FFA5'],
      created_at: subDays(new Date(), 1).toISOString(),
      is_daily_featured: true
    },
    {
      id: 'wallpaper-2',
      title: '宁静湖泊 - 内心平静',
      description: '平静如镜的湖面，倒映着蓝天白云，寓意内心的宁静与平和',
      category: '自然风光',
      theme: '治愈',
      resolution: '4K',
      size: '2.8MB',
      downloads: 12350,
      likes: 1956,
      tags: ['湖泊', '宁静', '倒影', '平和'],
      preview_url: 'https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=800&h=600&fit=crop',
      download_urls: {
        '1080p': 'https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=1920&h=1080&fit=crop',
        '2K': 'https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=2560&h=1440&fit=crop',
        '4K': 'https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=3840&h=2160&fit=crop'
      },
      quote: '内心平静，外界自然和谐',
      color_palette: ['#4A90E2', '#7ED321', '#50E3C2', '#B8E986'],
      created_at: subDays(new Date(), 2).toISOString(),
      is_daily_featured: false
    },
    {
      id: 'wallpaper-3',
      title: '星空银河 - 无限可能',
      description: '璀璨的银河星空，提醒我们宇宙的浩瀚和人生的无限可能',
      category: '星空宇宙',
      theme: '励志',
      resolution: '4K',
      size: '4.1MB',
      downloads: 18750,
      likes: 3421,
      tags: ['星空', '银河', '宇宙', '梦想'],
      preview_url: 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=800&h=600&fit=crop',
      download_urls: {
        '1080p': 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=1920&h=1080&fit=crop',
        '2K': 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=2560&h=1440&fit=crop',
        '4K': 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=3840&h=2160&fit=crop'
      },
      quote: '仰望星空，脚踏实地',
      color_palette: ['#1A1A2E', '#16213E', '#0F3460', '#533483'],
      created_at: subDays(new Date(), 3).toISOString(),
      is_daily_featured: false
    },
    {
      id: 'wallpaper-4',
      title: '森林小径 - 前行之路',
      description: '阳光透过树叶洒在小径上，象征着人生路上的光明与希望',
      category: '自然风光',
      theme: '成长',
      resolution: '4K',
      size: '3.5MB',
      downloads: 9876,
      likes: 1654,
      tags: ['森林', '小径', '阳光', '前行'],
      preview_url: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop',
      download_urls: {
        '1080p': 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=1920&h=1080&fit=crop',
        '2K': 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=2560&h=1440&fit=crop',
        '4K': 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=3840&h=2160&fit=crop'
      },
      quote: '路虽远，行则将至',
      color_palette: ['#2ECC71', '#27AE60', '#F39C12', '#E67E22'],
      created_at: subDays(new Date(), 4).toISOString(),
      is_daily_featured: false
    },
    {
      id: 'wallpaper-5',
      title: '简约几何 - 专注力量',
      description: '简洁的几何图形设计，体现专注的力量和简约的美学',
      category: '简约设计',
      theme: '专注',
      resolution: '4K',
      size: '1.2MB',
      downloads: 7654,
      likes: 1234,
      tags: ['几何', '简约', '专注', '设计'],
      preview_url: 'https://images.unsplash.com/photo-1557683316-973673baf926?w=800&h=600&fit=crop',
      download_urls: {
        '1080p': 'https://images.unsplash.com/photo-1557683316-973673baf926?w=1920&h=1080&fit=crop',
        '2K': 'https://images.unsplash.com/photo-1557683316-973673baf926?w=2560&h=1440&fit=crop',
        '4K': 'https://images.unsplash.com/photo-1557683316-973673baf926?w=3840&h=2160&fit=crop'
      },
      quote: '简约而不简单，专注成就卓越',
      color_palette: ['#3498DB', '#2980B9', '#ECF0F1', '#BDC3C7'],
      created_at: subDays(new Date(), 5).toISOString(),
      is_daily_featured: false
    },
    {
      id: 'wallpaper-6',
      title: '海浪拍岸 - 坚持不懈',
      description: '海浪不断拍打岸边，象征着坚持不懈的精神和持续的努力',
      category: '自然风光',
      theme: '坚持',
      resolution: '4K',
      size: '3.8MB',
      downloads: 11234,
      likes: 2156,
      tags: ['海浪', '海岸', '坚持', '力量'],
      preview_url: 'https://images.unsplash.com/photo-1505142468610-359e7d316be0?w=800&h=600&fit=crop',
      download_urls: {
        '1080p': 'https://images.unsplash.com/photo-1505142468610-359e7d316be0?w=1920&h=1080&fit=crop',
        '2K': 'https://images.unsplash.com/photo-1505142468610-359e7d316be0?w=2560&h=1440&fit=crop',
        '4K': 'https://images.unsplash.com/photo-1505142468610-359e7d316be0?w=3840&h=2160&fit=crop'
      },
      quote: '水滴石穿，坚持的力量无穷',
      color_palette: ['#3498DB', '#2980B9', '#ECF0F1', '#34495E'],
      created_at: subDays(new Date(), 6).toISOString(),
      is_daily_featured: false
    }
  ]

  return wallpapers
}

// 模拟API延迟
export const mockApiDelay = (ms: number = 500) =>
  new Promise(resolve => setTimeout(resolve, ms))

// 模拟API响应
export async function mockApiResponse<T>(data: T, delay: number = 500): Promise<{ data: T; error: null }> {
  await mockApiDelay(delay)
  return { data, error: null }
}
