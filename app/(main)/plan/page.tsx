'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  Target,
  Calendar,
  CheckCircle,
  Clock,
  TrendingUp,
  Settings,
  Play,
  Pause,
  RotateCcw,
  Zap,
  Award,
  BookOpen
} from 'lucide-react'
import { mockApiResponse, generateMockUserStats } from '@/lib/mock-data'
import { formatDate, getTodayString } from '@/lib/utils'
import type { RecoveryPlan, DailyTask, Milestone, UserStats } from '@/types'

export default function PlanPage() {
  const [plan, setPlan] = useState<RecoveryPlan | null>(null)
  const [todayTasks, setTodayTasks] = useState<DailyTask[]>([])
  const [milestones, setMilestones] = useState<Milestone[]>([])
  const [stats, setStats] = useState<UserStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [planStatus, setPlanStatus] = useState<'active' | 'paused' | 'completed'>('active')

  useEffect(() => {
    loadPlanData()
  }, [])

  const loadPlanData = async () => {
    try {
      // 模拟加载计划数据
      const mockPlan: RecoveryPlan = {
        id: 'plan-123',
        user_id: 'mock-user-123',
        status: 'active',
        plan_data: {
          duration_days: 90,
          daily_tasks: [
            {
              id: 'task-1',
              title: '晨间冥想',
              description: '进行10分钟的正念冥想，专注呼吸，平静内心',
              category: 'mindfulness',
              difficulty: 2,
              estimated_minutes: 10,
              completed: false,
              task_date: getTodayString()
            },
            {
              id: 'task-2',
              title: '户外运动',
              description: '进行30分钟的户外运动，如跑步、散步或骑行',
              category: 'physical',
              difficulty: 3,
              estimated_minutes: 30,
              completed: true,
              task_date: getTodayString()
            },
            {
              id: 'task-3',
              title: '阅读学习',
              description: '阅读一篇关于自我提升的文章或书籍章节',
              category: 'learning',
              difficulty: 2,
              estimated_minutes: 20,
              completed: false,
              task_date: getTodayString()
            },
            {
              id: 'task-4',
              title: '情绪记录',
              description: '记录今天的情绪状态和感受，进行自我反思',
              category: 'mindfulness',
              difficulty: 1,
              estimated_minutes: 5,
              completed: false,
              task_date: getTodayString()
            }
          ],
          milestones: [
            {
              id: 'milestone-1',
              title: '第一周',
              description: '成功坚持一周，建立初步习惯',
              target_days: 7,
              achieved: true,
              achieved_at: '2024-01-07T00:00:00Z'
            },
            {
              id: 'milestone-2',
              title: '第一个月',
              description: '坚持一个月，证明你的决心',
              target_days: 30,
              achieved: true,
              achieved_at: '2024-01-30T00:00:00Z'
            },
            {
              id: 'milestone-3',
              title: '三个月',
              description: '坚持三个月，习惯已经养成',
              target_days: 90,
              achieved: false
            },
            {
              id: 'milestone-4',
              title: '半年',
              description: '坚持半年，生活发生质的改变',
              target_days: 180,
              achieved: false
            }
          ],
          strategies: [
            {
              id: 'strategy-1',
              title: '深呼吸技巧',
              description: '当感到冲动时，进行4-7-8呼吸法',
              category: 'intervention',
              triggers: ['压力大', '情绪波动']
            },
            {
              id: 'strategy-2',
              title: '转移注意力',
              description: '立即离开当前环境，进行其他活动',
              category: 'prevention',
              triggers: ['深夜独处', '无聊']
            }
          ]
        },
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }

      const [planResponse, statsResponse] = await Promise.all([
        mockApiResponse(mockPlan),
        mockApiResponse(generateMockUserStats())
      ])

      setPlan(planResponse.data)
      setTodayTasks(planResponse.data.plan_data.daily_tasks)
      setMilestones(planResponse.data.plan_data.milestones)
      setStats(statsResponse.data)
      setPlanStatus(planResponse.data.status)
    } catch (error) {
      console.error('加载计划数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTaskComplete = async (taskId: string) => {
    try {
      await mockApiResponse({ success: true })
      setTodayTasks(prev => 
        prev.map(task => 
          task.id === taskId 
            ? { ...task, completed: true }
            : task
        )
      )
    } catch (error) {
      console.error('完成任务失败:', error)
    }
  }

  const handlePlanStatusChange = async (newStatus: 'active' | 'paused') => {
    try {
      await mockApiResponse({ success: true })
      setPlanStatus(newStatus)
    } catch (error) {
      console.error('更新计划状态失败:', error)
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'mindfulness': return <Zap className="h-4 w-4 text-purple-500" />
      case 'physical': return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'learning': return <BookOpen className="h-4 w-4 text-blue-500" />
      case 'hobby': return <Award className="h-4 w-4 text-yellow-500" />
      default: return <Target className="h-4 w-4 text-gray-500" />
    }
  }

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'mindfulness': return '正念冥想'
      case 'physical': return '身体锻炼'
      case 'learning': return '学习成长'
      case 'hobby': return '兴趣爱好'
      default: return '其他'
    }
  }

  const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
      case 1: return 'bg-green-100 text-green-800'
      case 2: return 'bg-blue-100 text-blue-800'
      case 3: return 'bg-yellow-100 text-yellow-800'
      case 4: return 'bg-orange-100 text-orange-800'
      case 5: return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-96 bg-gray-200 rounded-lg"></div>
            <div className="h-96 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    )
  }

  const completedTasks = todayTasks.filter(task => task.completed).length
  const totalTasks = todayTasks.length
  const taskProgress = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0
  const achievedMilestones = milestones.filter(m => m.achieved).length

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">我的戒色计划</h1>
          <p className="text-gray-600">个性化的90天戒色计划，助您重塑健康生活</p>
        </div>
        <div className="flex gap-2 mt-4 sm:mt-0">
          {planStatus === 'active' ? (
            <Button
              variant="outline"
              onClick={() => handlePlanStatusChange('paused')}
            >
              <Pause className="mr-2 h-4 w-4" />
              暂停计划
            </Button>
          ) : (
            <Button
              variant="gradient"
              onClick={() => handlePlanStatusChange('active')}
            >
              <Play className="mr-2 h-4 w-4" />
              继续计划
            </Button>
          )}
          <Button variant="outline" onClick={() => window.location.href = '/plan/adjust'}>
            <Settings className="mr-2 h-4 w-4" />
            调整计划
          </Button>
          <Button variant="outline" onClick={() => window.location.href = '/plan/create'}>
            <Target className="mr-2 h-4 w-4" />
            新建计划
          </Button>
          <Button variant="outline" onClick={() => window.location.href = '/plan/history'}>
            <Calendar className="mr-2 h-4 w-4" />
            历史计划
          </Button>
        </div>
      </div>

      {/* 计划概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">计划进度</CardTitle>
            <Calendar className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.current_streak || 0}/{plan?.plan_data.duration_days || 90}</div>
            <Progress value={(stats?.current_streak || 0) / (plan?.plan_data.duration_days || 90) * 100} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日任务</CardTitle>
            <Target className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedTasks}/{totalTasks}</div>
            <Progress value={taskProgress} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">里程碑</CardTitle>
            <Award className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{achievedMilestones}/{milestones.length}</div>
            <p className="text-xs text-muted-foreground">
              已达成 {achievedMilestones} 个目标
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">计划状态</CardTitle>
            {planStatus === 'active' ? 
              <Play className="h-4 w-4 text-green-500" /> : 
              <Pause className="h-4 w-4 text-yellow-500" />
            }
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${
              planStatus === 'active' ? 'text-green-600' : 'text-yellow-600'
            }`}>
              {planStatus === 'active' ? '进行中' : '已暂停'}
            </div>
            <p className="text-xs text-muted-foreground">
              {planStatus === 'active' ? '保持专注' : '随时可以重启'}
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 今日任务 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="mr-2 h-5 w-5" />
              今日任务
            </CardTitle>
            <CardDescription>
              完成今天的个性化任务，保持良好习惯
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {todayTasks.map((task) => (
                <div
                  key={task.id}
                  className={`p-4 rounded-lg border transition-all ${
                    task.completed
                      ? 'bg-green-50 border-green-200'
                      : 'bg-white border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        {getCategoryIcon(task.category)}
                        <h4 className={`font-medium ${
                          task.completed ? 'text-green-700 line-through' : 'text-gray-900'
                        }`}>
                          {task.title}
                        </h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(task.difficulty)}`}>
                          难度 {task.difficulty}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        {task.description}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <div className="flex items-center">
                          <Clock className="mr-1 h-3 w-3" />
                          {task.estimated_minutes} 分钟
                        </div>
                        <div className="flex items-center">
                          <span className="mr-1">📂</span>
                          {getCategoryLabel(task.category)}
                        </div>
                      </div>
                    </div>
                    <div className="ml-4">
                      {!task.completed ? (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleTaskComplete(task.id)}
                        >
                          完成
                        </Button>
                      ) : (
                        <CheckCircle className="h-6 w-6 text-green-500" />
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {todayTasks.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Target className="mx-auto h-12 w-12 mb-2" />
                  <p>今日暂无任务</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 里程碑进度 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="mr-2 h-5 w-5" />
              里程碑进度
            </CardTitle>
            <CardDescription>
              追踪您的重要成就和目标
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {milestones.map((milestone, index) => (
                <div
                  key={milestone.id}
                  className={`p-4 rounded-lg border ${
                    milestone.achieved
                      ? 'bg-yellow-50 border-yellow-200'
                      : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className={`font-medium ${
                      milestone.achieved ? 'text-yellow-700' : 'text-gray-900'
                    }`}>
                      {milestone.title}
                    </h4>
                    {milestone.achieved ? (
                      <div className="flex items-center text-yellow-600">
                        <Award className="h-4 w-4 mr-1" />
                        <span className="text-xs font-medium">已达成</span>
                      </div>
                    ) : (
                      <span className="text-xs text-gray-500">
                        目标: {milestone.target_days} 天
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    {milestone.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex-1 mr-4">
                      <Progress
                        value={milestone.achieved ? 100 : Math.min((stats?.current_streak || 0) / milestone.target_days * 100, 100)}
                        className="h-2"
                      />
                    </div>
                    <span className="text-xs text-gray-500">
                      {milestone.achieved
                        ? `✓ ${formatDate(milestone.achieved_at!, 'MM/dd')}`
                        : `${Math.min(stats?.current_streak || 0, milestone.target_days)}/${milestone.target_days}`
                      }
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 应对策略 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="mr-2 h-5 w-5" />
            应对策略
          </CardTitle>
          <CardDescription>
            当遇到挑战时，使用这些科学有效的应对方法
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {plan?.plan_data.strategies.map((strategy) => (
              <div
                key={strategy.id}
                className="p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
              >
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{strategy.title}</h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    strategy.category === 'prevention' ? 'bg-blue-100 text-blue-800' :
                    strategy.category === 'intervention' ? 'bg-red-100 text-red-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {strategy.category === 'prevention' ? '预防' :
                     strategy.category === 'intervention' ? '干预' : '恢复'}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-3">
                  {strategy.description}
                </p>
                <div className="flex flex-wrap gap-1">
                  {strategy.triggers.map((trigger, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                    >
                      {trigger}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
