'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  ArrowLeft,
  Calendar,
  Target,
  Clock,
  CheckCircle,
  XCircle,
  Pause,
  Play,
  RotateCcw,
  TrendingUp,
  Award,
  Filter,
  Search
} from 'lucide-react'
import Link from 'next/link'
import { Input } from '@/components/ui/input'
import { mockApiResponse } from '@/lib/mock-data'
import { formatDate } from '@/lib/utils'

interface HistoricalPlan {
  id: string
  name: string
  type: string
  status: 'completed' | 'failed' | 'paused' | 'active'
  duration_days: number
  completed_days: number
  success_rate: number
  created_at: string
  completed_at?: string
  goals: string[]
  achievements: string[]
  notes?: string
}

export default function PlanHistoryPage() {
  const [plans, setPlans] = useState<HistoricalPlan[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'completed' | 'failed' | 'paused'>('all')
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'success_rate'>('newest')

  useEffect(() => {
    loadHistoricalPlans()
  }, [])

  const loadHistoricalPlans = async () => {
    try {
      const mockPlans: HistoricalPlan[] = [
        {
          id: 'plan-1',
          name: '90天戒色计划',
          type: 'quit_porn',
          status: 'completed',
          duration_days: 90,
          completed_days: 90,
          success_rate: 100,
          created_at: '2024-01-01T00:00:00Z',
          completed_at: '2024-03-31T00:00:00Z',
          goals: ['提升专注力', '改善睡眠质量', '增强自信心'],
          achievements: ['连续90天成功', '获得坚持者徽章', '完成所有里程碑'],
          notes: '非常成功的一次计划，感觉整个人都焕然一新！'
        },
        {
          id: 'plan-2',
          name: '30天戒熬夜计划',
          type: 'quit_staying_up',
          status: 'failed',
          duration_days: 30,
          completed_days: 18,
          success_rate: 60,
          created_at: '2024-02-01T00:00:00Z',
          goals: ['11点前睡觉', '提升睡眠质量', '改善精神状态'],
          achievements: ['连续7天成功', '获得早睡新手徽章'],
          notes: '工作压力太大，后期没能坚持下来，下次要制定更合理的计划。'
        },
        {
          id: 'plan-3',
          name: '60天戒烟计划',
          type: 'quit_smoking',
          status: 'paused',
          duration_days: 60,
          completed_days: 25,
          success_rate: 42,
          created_at: '2024-03-01T00:00:00Z',
          goals: ['完全戒烟', '改善肺部健康', '节省开支'],
          achievements: ['连续14天成功', '获得健康生活徽章'],
          notes: '因为出差暂停了计划，准备重新开始。'
        },
        {
          id: 'plan-4',
          name: '21天戒咖啡因计划',
          type: 'quit_coffee',
          status: 'completed',
          duration_days: 21,
          completed_days: 21,
          success_rate: 100,
          created_at: '2024-04-01T00:00:00Z',
          completed_at: '2024-04-21T00:00:00Z',
          goals: ['戒除咖啡依赖', '改善睡眠', '减少焦虑'],
          achievements: ['连续21天成功', '获得自然精力徽章', '完美完成'],
          notes: '比想象中容易，现在精力更加稳定了。'
        },
        {
          id: 'plan-5',
          name: '45天戒游戏计划',
          type: 'quit_gaming',
          status: 'active',
          duration_days: 45,
          completed_days: 12,
          success_rate: 27,
          created_at: '2024-05-01T00:00:00Z',
          goals: ['控制游戏时间', '专注学习工作', '培养其他兴趣'],
          achievements: ['连续5天成功'],
          notes: '目前进展良好，需要继续坚持。'
        }
      ]

      const response = await mockApiResponse(mockPlans)
      setPlans(response.data)
    } catch (error) {
      console.error('加载历史计划失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'completed': return '已完成'
      case 'failed': return '已失败'
      case 'paused': return '已暂停'
      case 'active': return '进行中'
      default: return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'failed': return 'bg-red-100 text-red-800'
      case 'paused': return 'bg-yellow-100 text-yellow-800'
      case 'active': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />
      case 'failed': return <XCircle className="h-4 w-4" />
      case 'paused': return <Pause className="h-4 w-4" />
      case 'active': return <Play className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const getPlanTypeLabel = (type: string) => {
    const typeMap: Record<string, string> = {
      'quit_porn': '戒色计划',
      'quit_smoking': '戒烟计划',
      'quit_drinking': '戒酒计划',
      'quit_staying_up': '戒熬夜计划',
      'quit_gaming': '戒游戏计划',
      'quit_phone': '戒手机依赖',
      'quit_coffee': '戒咖啡因',
      'quit_shopping': '戒购物冲动'
    }
    return typeMap[type] || type
  }

  const filteredPlans = plans.filter(plan => {
    const matchesSearch = plan.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         getPlanTypeLabel(plan.type).toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = statusFilter === 'all' || plan.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const sortedPlans = [...filteredPlans].sort((a, b) => {
    switch (sortBy) {
      case 'newest':
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      case 'oldest':
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      case 'success_rate':
        return b.success_rate - a.success_rate
      default:
        return 0
    }
  })

  const handleRestartPlan = async (planId: string) => {
    try {
      // 这里会调用API重启计划
      console.log('重启计划:', planId)
      await mockApiResponse({ success: true })
      // 重新加载数据
      loadHistoricalPlans()
    } catch (error) {
      console.error('重启计划失败:', error)
    }
  }

  const getOverallStats = () => {
    const totalPlans = plans.length
    const completedPlans = plans.filter(p => p.status === 'completed').length
    const averageSuccessRate = plans.reduce((sum, p) => sum + p.success_rate, 0) / totalPlans || 0
    const totalDays = plans.reduce((sum, p) => sum + p.completed_days, 0)

    return {
      totalPlans,
      completedPlans,
      averageSuccessRate: Math.round(averageSuccessRate),
      totalDays
    }
  }

  const stats = getOverallStats()

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载历史计划中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <Link href="/plan" className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-2">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回计划
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">历史计划</h1>
          <p className="text-gray-600">回顾你的成长历程，总结经验教训</p>
        </div>
      </div>

      {/* 统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总计划数</CardTitle>
            <Target className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalPlans}</div>
            <p className="text-xs text-muted-foreground">
              已创建的计划总数
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">完成计划</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedPlans}</div>
            <p className="text-xs text-muted-foreground">
              成功完成的计划
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均成功率</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageSuccessRate}%</div>
            <p className="text-xs text-muted-foreground">
              所有计划的平均完成度
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">累计坚持</CardTitle>
            <Award className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalDays}</div>
            <p className="text-xs text-muted-foreground">
              总坚持天数
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            筛选和搜索
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="搜索计划名称或类型..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="px-3 py-2 border rounded-md text-sm"
              >
                <option value="all">所有状态</option>
                <option value="completed">已完成</option>
                <option value="failed">已失败</option>
                <option value="paused">已暂停</option>
                <option value="active">进行中</option>
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border rounded-md text-sm"
              >
                <option value="newest">最新创建</option>
                <option value="oldest">最早创建</option>
                <option value="success_rate">成功率</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 计划列表 */}
      <div className="space-y-4">
        {sortedPlans.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无历史计划</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery || statusFilter !== 'all'
                  ? '没有找到符合条件的计划'
                  : '你还没有创建过计划'}
              </p>
              <Link href="/plan/create">
                <Button>
                  <Target className="mr-2 h-4 w-4" />
                  创建第一个计划
                </Button>
              </Link>
            </CardContent>
          </Card>
        ) : (
          sortedPlans.map((plan) => (
            <Card key={plan.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{plan.name}</h3>
                      <Badge variant="outline">{getPlanTypeLabel(plan.type)}</Badge>
                      <Badge className={getStatusColor(plan.status)}>
                        {getStatusIcon(plan.status)}
                        <span className="ml-1">{getStatusLabel(plan.status)}</span>
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                      <span className="flex items-center">
                        <Calendar className="mr-1 h-4 w-4" />
                        {formatDate(plan.created_at)}
                      </span>
                      {plan.completed_at && (
                        <span className="flex items-center">
                          <CheckCircle className="mr-1 h-4 w-4" />
                          {formatDate(plan.completed_at)}
                        </span>
                      )}
                      <span className="flex items-center">
                        <Clock className="mr-1 h-4 w-4" />
                        {plan.completed_days}/{plan.duration_days} 天
                      </span>
                    </div>
                    <div className="mb-3">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">完成进度</span>
                        <span className="text-sm text-gray-600">{plan.success_rate}%</span>
                      </div>
                      <Progress value={plan.success_rate} className="h-2" />
                    </div>
                  </div>
                </div>

                {/* 目标和成就 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">计划目标</h4>
                    <div className="flex flex-wrap gap-1">
                      {plan.goals.map((goal, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {goal}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">获得成就</h4>
                    <div className="flex flex-wrap gap-1">
                      {plan.achievements.map((achievement, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          <Award className="mr-1 h-3 w-3" />
                          {achievement}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>

                {/* 备注 */}
                {plan.notes && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">计划总结</h4>
                    <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                      {plan.notes}
                    </p>
                  </div>
                )}

                {/* 操作按钮 */}
                <div className="flex gap-2">
                  {(plan.status === 'failed' || plan.status === 'paused') && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRestartPlan(plan.id)}
                    >
                      <RotateCcw className="mr-2 h-4 w-4" />
                      重新开始
                    </Button>
                  )}
                  <Button variant="ghost" size="sm">
                    查看详情
                  </Button>
                  <Button variant="ghost" size="sm">
                    复制计划
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
