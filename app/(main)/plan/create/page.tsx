'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft,
  Target,
  Calendar,
  Clock,
  Zap,
  Heart,
  Moon,
  Cigarette,
  Wine,
  Gamepad2,
  Smartphone,
  Coffee,
  ShoppingBag,
  Plus,
  X
} from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

interface PlanType {
  id: string
  name: string
  icon: React.ReactNode
  description: string
  color: string
  defaultDuration: number
  category: 'health' | 'lifestyle' | 'addiction'
}

const PLAN_TYPES: PlanType[] = [
  {
    id: 'quit_porn',
    name: '戒色计划',
    icon: <Heart className="h-6 w-6" />,
    description: '戒除色情内容，重塑健康生活',
    color: 'bg-red-100 text-red-800 border-red-200',
    defaultDuration: 90,
    category: 'health'
  },
  {
    id: 'quit_smoking',
    name: '戒烟计划',
    icon: <Cigarette className="h-6 w-6" />,
    description: '戒除烟草，保护身体健康',
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    defaultDuration: 60,
    category: 'health'
  },
  {
    id: 'quit_drinking',
    name: '戒酒计划',
    icon: <Wine className="h-6 w-6" />,
    description: '戒除酒精，恢复清醒生活',
    color: 'bg-purple-100 text-purple-800 border-purple-200',
    defaultDuration: 90,
    category: 'health'
  },
  {
    id: 'quit_staying_up',
    name: '戒熬夜计划',
    icon: <Moon className="h-6 w-6" />,
    description: '规律作息，提升睡眠质量',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    defaultDuration: 30,
    category: 'lifestyle'
  },
  {
    id: 'quit_gaming',
    name: '戒游戏计划',
    icon: <Gamepad2 className="h-6 w-6" />,
    description: '控制游戏时间，专注现实生活',
    color: 'bg-green-100 text-green-800 border-green-200',
    defaultDuration: 60,
    category: 'addiction'
  },
  {
    id: 'quit_phone',
    name: '戒手机依赖',
    icon: <Smartphone className="h-6 w-6" />,
    description: '减少手机使用，提升专注力',
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    defaultDuration: 30,
    category: 'lifestyle'
  },
  {
    id: 'quit_coffee',
    name: '戒咖啡因',
    icon: <Coffee className="h-6 w-6" />,
    description: '戒除咖啡因依赖，自然精力',
    color: 'bg-amber-100 text-amber-800 border-amber-200',
    defaultDuration: 21,
    category: 'health'
  },
  {
    id: 'quit_shopping',
    name: '戒购物冲动',
    icon: <ShoppingBag className="h-6 w-6" />,
    description: '控制消费欲望，理性购物',
    color: 'bg-pink-100 text-pink-800 border-pink-200',
    defaultDuration: 60,
    category: 'lifestyle'
  }
]

export default function CreatePlanPage() {
  const router = useRouter()
  const [selectedPlanType, setSelectedPlanType] = useState<PlanType | null>(null)
  const [planName, setPlanName] = useState('')
  const [planDescription, setPlanDescription] = useState('')
  const [duration, setDuration] = useState(90)
  const [goals, setGoals] = useState<string[]>([])
  const [newGoal, setNewGoal] = useState('')
  const [triggers, setTriggers] = useState<string[]>([])
  const [newTrigger, setNewTrigger] = useState('')
  const [difficulty, setDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handlePlanTypeSelect = (planType: PlanType) => {
    setSelectedPlanType(planType)
    setPlanName(planType.name)
    setPlanDescription(planType.description)
    setDuration(planType.defaultDuration)
  }

  const addGoal = () => {
    if (newGoal.trim() && !goals.includes(newGoal.trim())) {
      setGoals([...goals, newGoal.trim()])
      setNewGoal('')
    }
  }

  const removeGoal = (goal: string) => {
    setGoals(goals.filter(g => g !== goal))
  }

  const addTrigger = () => {
    if (newTrigger.trim() && !triggers.includes(newTrigger.trim())) {
      setTriggers([...triggers, newTrigger.trim()])
      setNewTrigger('')
    }
  }

  const removeTrigger = (trigger: string) => {
    setTriggers(triggers.filter(t => t !== trigger))
  }

  const handleSubmit = async () => {
    if (!selectedPlanType || !planName.trim()) return

    setIsSubmitting(true)
    try {
      // 这里会调用API创建计划
      const planData = {
        type: selectedPlanType.id,
        name: planName,
        description: planDescription,
        duration,
        goals,
        triggers,
        difficulty,
        created_at: new Date().toISOString()
      }

      console.log('创建计划:', planData)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 跳转到计划详情页
      router.push('/plan')
    } catch (error) {
      console.error('创建计划失败:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'health': return '健康类'
      case 'lifestyle': return '生活方式'
      case 'addiction': return '成瘾戒除'
      default: return '其他'
    }
  }

  const getDifficultyLabel = (level: string) => {
    switch (level) {
      case 'easy': return '简单'
      case 'medium': return '中等'
      case 'hard': return '困难'
      default: return '中等'
    }
  }

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'easy': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'hard': return 'bg-red-100 text-red-800'
      default: return 'bg-yellow-100 text-yellow-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <Link href="/plan" className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-2">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回计划
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">创建新计划</h1>
          <p className="text-gray-600">选择适合你的计划类型，开始改变之旅</p>
        </div>
      </div>

      {/* 步骤1：选择计划类型 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="mr-2 h-5 w-5" />
            步骤1：选择计划类型
          </CardTitle>
          <CardDescription>
            选择你想要改变的习惯或行为
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {PLAN_TYPES.map((planType) => (
              <div
                key={planType.id}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ${
                  selectedPlanType?.id === planType.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handlePlanTypeSelect(planType)}
              >
                <div className="flex items-start gap-3">
                  <div className={`p-2 rounded-lg ${planType.color}`}>
                    {planType.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium text-gray-900">{planType.name}</h3>
                      <Badge variant="outline" className="text-xs">
                        {getCategoryLabel(planType.category)}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{planType.description}</p>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Clock className="h-3 w-3" />
                      <span>建议 {planType.defaultDuration} 天</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 步骤2：计划详情 */}
      {selectedPlanType && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="mr-2 h-5 w-5" />
              步骤2：计划详情
            </CardTitle>
            <CardDescription>
              自定义你的计划内容和目标
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 计划名称 */}
            <div className="space-y-2">
              <Label htmlFor="planName">计划名称</Label>
              <Input
                id="planName"
                value={planName}
                onChange={(e) => setPlanName(e.target.value)}
                placeholder="给你的计划起个名字"
              />
            </div>

            {/* 计划描述 */}
            <div className="space-y-2">
              <Label htmlFor="planDescription">计划描述</Label>
              <Textarea
                id="planDescription"
                value={planDescription}
                onChange={(e) => setPlanDescription(e.target.value)}
                placeholder="描述你的计划目标和动机"
                rows={3}
              />
            </div>

            {/* 计划时长 */}
            <div className="space-y-2">
              <Label htmlFor="duration">计划时长（天）</Label>
              <Input
                id="duration"
                type="number"
                value={duration}
                onChange={(e) => setDuration(parseInt(e.target.value) || 0)}
                min="1"
                max="365"
              />
            </div>

            {/* 难度选择 */}
            <div className="space-y-2">
              <Label>计划难度</Label>
              <div className="flex gap-3">
                {(['easy', 'medium', 'hard'] as const).map((level) => (
                  <button
                    key={level}
                    className={`px-4 py-2 rounded-lg border-2 transition-all ${
                      difficulty === level
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setDifficulty(level)}
                  >
                    <Badge className={getDifficultyColor(level)}>
                      {getDifficultyLabel(level)}
                    </Badge>
                  </button>
                ))}
              </div>
            </div>

            {/* 计划目标 */}
            <div className="space-y-2">
              <Label>计划目标</Label>
              <div className="flex gap-2">
                <Input
                  value={newGoal}
                  onChange={(e) => setNewGoal(e.target.value)}
                  placeholder="添加一个具体目标"
                  onKeyPress={(e) => e.key === 'Enter' && addGoal()}
                />
                <Button type="button" onClick={addGoal} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {goals.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {goals.map((goal, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {goal}
                      <button
                        onClick={() => removeGoal(goal)}
                        className="ml-1 hover:text-red-500"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* 触发因素 */}
            <div className="space-y-2">
              <Label>主要触发因素</Label>
              <div className="flex gap-2">
                <Input
                  value={newTrigger}
                  onChange={(e) => setNewTrigger(e.target.value)}
                  placeholder="添加可能的触发因素"
                  onKeyPress={(e) => e.key === 'Enter' && addTrigger()}
                />
                <Button type="button" onClick={addTrigger} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {triggers.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {triggers.map((trigger, index) => (
                    <Badge key={index} variant="outline" className="flex items-center gap-1">
                      {trigger}
                      <button
                        onClick={() => removeTrigger(trigger)}
                        className="ml-1 hover:text-red-500"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 创建按钮 */}
      {selectedPlanType && (
        <div className="flex justify-end gap-3">
          <Link href="/plan">
            <Button variant="outline">取消</Button>
          </Link>
          <Button
            onClick={handleSubmit}
            disabled={!planName.trim() || isSubmitting}
            className="min-w-[120px]"
          >
            {isSubmitting ? '创建中...' : '创建计划'}
          </Button>
        </div>
      )}
    </div>
  )
}
