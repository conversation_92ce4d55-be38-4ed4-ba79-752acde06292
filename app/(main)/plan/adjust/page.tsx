'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { 
  ArrowLeft,
  Settings,
  Target,
  Calendar,
  Clock,
  Zap,
  Plus,
  X,
  Save,
  RotateCcw,
  AlertTriangle
} from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { mockApiResponse } from '@/lib/mock-data'

interface CurrentPlan {
  id: string
  name: string
  type: string
  description: string
  duration_days: number
  completed_days: number
  goals: string[]
  triggers: string[]
  difficulty: 'easy' | 'medium' | 'hard'
  daily_tasks: Array<{
    id: string
    title: string
    description: string
    category: string
    enabled: boolean
    difficulty: number
  }>
  strategies: Array<{
    id: string
    title: string
    description: string
    enabled: boolean
  }>
}

export default function AdjustPlanPage() {
  const router = useRouter()
  const [plan, setPlan] = useState<CurrentPlan | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [planName, setPlanName] = useState('')
  const [planDescription, setPlanDescription] = useState('')
  const [duration, setDuration] = useState(90)
  const [goals, setGoals] = useState<string[]>([])
  const [newGoal, setNewGoal] = useState('')
  const [triggers, setTriggers] = useState<string[]>([])
  const [newTrigger, setNewTrigger] = useState('')
  const [difficulty, setDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium')
  const [dailyTasks, setDailyTasks] = useState<CurrentPlan['daily_tasks']>([])
  const [strategies, setStrategies] = useState<CurrentPlan['strategies']>([])

  useEffect(() => {
    loadCurrentPlan()
  }, [])

  const loadCurrentPlan = async () => {
    try {
      const mockPlan: CurrentPlan = {
        id: 'plan-123',
        name: '90天戒色计划',
        type: 'quit_porn',
        description: '通过科学的方法和坚持不懈的努力，重塑健康生活',
        duration_days: 90,
        completed_days: 23,
        goals: ['提升专注力', '改善睡眠质量', '增强自信心'],
        triggers: ['深夜独处', '压力大', '无聊'],
        difficulty: 'medium',
        daily_tasks: [
          {
            id: 'task-1',
            title: '晨间冥想',
            description: '进行10分钟的正念冥想',
            category: 'mindfulness',
            enabled: true,
            difficulty: 2
          },
          {
            id: 'task-2',
            title: '户外运动',
            description: '进行30分钟的户外运动',
            category: 'physical',
            enabled: true,
            difficulty: 3
          },
          {
            id: 'task-3',
            title: '阅读学习',
            description: '阅读一篇关于自我提升的文章',
            category: 'learning',
            enabled: false,
            difficulty: 2
          }
        ],
        strategies: [
          {
            id: 'strategy-1',
            title: '深呼吸技巧',
            description: '当感到冲动时，进行4-7-8呼吸法',
            enabled: true
          },
          {
            id: 'strategy-2',
            title: '转移注意力',
            description: '立即离开当前环境，进行其他活动',
            enabled: true
          },
          {
            id: 'strategy-3',
            title: '冷水洗脸',
            description: '用冷水洗脸来快速清醒',
            enabled: false
          }
        ]
      }

      const response = await mockApiResponse(mockPlan)
      const planData = response.data
      setPlan(planData)
      setPlanName(planData.name)
      setPlanDescription(planData.description)
      setDuration(planData.duration_days)
      setGoals([...planData.goals])
      setTriggers([...planData.triggers])
      setDifficulty(planData.difficulty)
      setDailyTasks([...planData.daily_tasks])
      setStrategies([...planData.strategies])
    } catch (error) {
      console.error('加载计划失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const addGoal = () => {
    if (newGoal.trim() && !goals.includes(newGoal.trim())) {
      setGoals([...goals, newGoal.trim()])
      setNewGoal('')
    }
  }

  const removeGoal = (goal: string) => {
    setGoals(goals.filter(g => g !== goal))
  }

  const addTrigger = () => {
    if (newTrigger.trim() && !triggers.includes(newTrigger.trim())) {
      setTriggers([...triggers, newTrigger.trim()])
      setNewTrigger('')
    }
  }

  const removeTrigger = (trigger: string) => {
    setTriggers(triggers.filter(t => t !== trigger))
  }

  const toggleTaskEnabled = (taskId: string) => {
    setDailyTasks(prev => prev.map(task => 
      task.id === taskId ? { ...task, enabled: !task.enabled } : task
    ))
  }

  const toggleStrategyEnabled = (strategyId: string) => {
    setStrategies(prev => prev.map(strategy => 
      strategy.id === strategyId ? { ...strategy, enabled: !strategy.enabled } : strategy
    ))
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      const updatedPlan = {
        ...plan,
        name: planName,
        description: planDescription,
        duration_days: duration,
        goals,
        triggers,
        difficulty,
        daily_tasks: dailyTasks,
        strategies
      }

      await mockApiResponse({ success: true })
      console.log('计划已更新:', updatedPlan)
      router.push('/plan')
    } catch (error) {
      console.error('保存计划失败:', error)
    } finally {
      setSaving(false)
    }
  }

  const getDifficultyLabel = (level: string) => {
    switch (level) {
      case 'easy': return '简单'
      case 'medium': return '中等'
      case 'hard': return '困难'
      default: return '中等'
    }
  }

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'easy': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'hard': return 'bg-red-100 text-red-800'
      default: return 'bg-yellow-100 text-yellow-800'
    }
  }

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'mindfulness': return '正念冥想'
      case 'physical': return '体育锻炼'
      case 'learning': return '学习成长'
      case 'hobby': return '兴趣爱好'
      default: return category
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载计划中...</p>
        </div>
      </div>
    )
  }

  if (!plan) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到活跃的计划</h3>
        <p className="text-gray-600 mb-4">请先创建一个计划</p>
        <Link href="/plan/create">
          <Button>
            <Target className="mr-2 h-4 w-4" />
            创建计划
          </Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <Link href="/plan" className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-2">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回计划
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">调整计划</h1>
          <p className="text-gray-600">根据实际情况调整你的计划设置</p>
        </div>
      </div>

      {/* 警告提示 */}
      <Card className="border-yellow-200 bg-yellow-50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800">调整提醒</h3>
              <p className="text-sm text-yellow-700 mt-1">
                调整计划可能会影响你的进度统计。建议在必要时才进行调整，并确保新的设置更适合你的实际情况。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="mr-2 h-5 w-5" />
            基本信息
          </CardTitle>
          <CardDescription>
            调整计划的基本设置
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="planName">计划名称</Label>
            <Input
              id="planName"
              value={planName}
              onChange={(e) => setPlanName(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="planDescription">计划描述</Label>
            <Textarea
              id="planDescription"
              value={planDescription}
              onChange={(e) => setPlanDescription(e.target.value)}
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="duration">计划时长（天）</Label>
              <Input
                id="duration"
                type="number"
                value={duration}
                onChange={(e) => setDuration(parseInt(e.target.value) || 0)}
                min="1"
                max="365"
              />
            </div>

            <div className="space-y-2">
              <Label>计划难度</Label>
              <div className="flex gap-2">
                {(['easy', 'medium', 'hard'] as const).map((level) => (
                  <button
                    key={level}
                    className={`px-3 py-2 rounded-lg border-2 transition-all ${
                      difficulty === level
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setDifficulty(level)}
                  >
                    <Badge className={getDifficultyColor(level)}>
                      {getDifficultyLabel(level)}
                    </Badge>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 目标和触发因素 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="mr-2 h-5 w-5" />
              计划目标
            </CardTitle>
            <CardDescription>
              调整你的具体目标
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Input
                value={newGoal}
                onChange={(e) => setNewGoal(e.target.value)}
                placeholder="添加新目标"
                onKeyPress={(e) => e.key === 'Enter' && addGoal()}
              />
              <Button type="button" onClick={addGoal} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            {goals.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {goals.map((goal, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {goal}
                    <button
                      onClick={() => removeGoal(goal)}
                      className="ml-1 hover:text-red-500"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="mr-2 h-5 w-5" />
              触发因素
            </CardTitle>
            <CardDescription>
              识别和管理触发因素
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Input
                value={newTrigger}
                onChange={(e) => setNewTrigger(e.target.value)}
                placeholder="添加触发因素"
                onKeyPress={(e) => e.key === 'Enter' && addTrigger()}
              />
              <Button type="button" onClick={addTrigger} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            {triggers.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {triggers.map((trigger, index) => (
                  <Badge key={index} variant="outline" className="flex items-center gap-1">
                    {trigger}
                    <button
                      onClick={() => removeTrigger(trigger)}
                      className="ml-1 hover:text-red-500"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 每日任务管理 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            每日任务管理
          </CardTitle>
          <CardDescription>
            启用或禁用每日任务
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {dailyTasks.map((task) => (
              <div key={task.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-gray-900">{task.title}</h4>
                    <Badge variant="outline" className="text-xs">
                      {getCategoryLabel(task.category)}
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      难度 {task.difficulty}/5
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{task.description}</p>
                </div>
                <Switch
                  checked={task.enabled}
                  onCheckedChange={() => toggleTaskEnabled(task.id)}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 应对策略管理 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="mr-2 h-5 w-5" />
            应对策略管理
          </CardTitle>
          <CardDescription>
            启用或禁用应对策略
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {strategies.map((strategy) => (
              <div key={strategy.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">{strategy.title}</h4>
                  <p className="text-sm text-gray-600">{strategy.description}</p>
                </div>
                <Switch
                  checked={strategy.enabled}
                  onCheckedChange={() => toggleStrategyEnabled(strategy.id)}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 保存按钮 */}
      <div className="flex justify-end gap-3">
        <Link href="/plan">
          <Button variant="outline">取消</Button>
        </Link>
        <Button onClick={() => loadCurrentPlan()} variant="outline">
          <RotateCcw className="mr-2 h-4 w-4" />
          重置
        </Button>
        <Button
          onClick={handleSave}
          disabled={saving}
          className="min-w-[120px]"
        >
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              保存中...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              保存更改
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
