'use client'

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  ArrowLeft,
  BookOpen,
  Clock,
  Eye,
  Heart,
  Share2,
  Bookmark,
  ThumbsUp,
  MessageCircle,
  Star,
  Award,
  User
} from 'lucide-react'
import Link from 'next/link'
import { mockApiResponse } from '@/lib/mock-data'
import { formatDate } from '@/lib/utils'
import CommentSystem from '@/components/comments/CommentSystem'
import RewardSystem from '@/components/reward/RewardSystem'
import ShareDialog from '@/components/article/share-dialog'

interface InspirationArticle {
  id: string
  title: string
  content: string
  summary: string
  author: string
  author_id: string
  author_avatar?: string
  category: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  reading_time: number
  views: number
  likes: number
  bookmarks: number
  tags: string[]
  created_at: string
  updated_at: string
  is_featured: boolean
  is_liked: boolean
  is_bookmarked: boolean
}

export default function InspirationArticleDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { id } = params
  const [article, setArticle] = useState<InspirationArticle | null>(null)
  const [loading, setLoading] = useState(true)
  const [isLiked, setIsLiked] = useState(false)
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [likesCount, setLikesCount] = useState(0)
  const [bookmarksCount, setBookmarksCount] = useState(0)

  useEffect(() => {
    loadArticle()
  }, [id])

  const loadArticle = async () => {
    try {
      // 模拟从API加载文章数据
      const mockArticle: InspirationArticle = {
        id: id as string,
        title: '如何建立强大的内心力量：从自我认知开始',
        content: `
          <div class="prose prose-lg max-w-none">
            <h2>引言</h2>
            <p>在人生的旅途中，我们都会遇到各种挑战和困难。有些人能够坚韧不拔地面对困境，而有些人却容易被挫折击倒。这其中的差别往往在于内心力量的强弱。</p>
            
            <h2>什么是内心力量？</h2>
            <p>内心力量是指一个人面对困难、挫折和压力时所表现出的心理韧性和精神坚韧。它包括：</p>
            <ul>
              <li><strong>自我认知能力</strong>：了解自己的优势和不足</li>
              <li><strong>情绪调节能力</strong>：管理和控制自己的情绪</li>
              <li><strong>目标导向能力</strong>：明确目标并坚持不懈地追求</li>
              <li><strong>适应能力</strong>：在变化中保持平衡和稳定</li>
            </ul>
            
            <h2>如何培养内心力量？</h2>
            <h3>1. 深入了解自己</h3>
            <p>自我认知是建立内心力量的基础。通过反思和自省，我们可以：</p>
            <ul>
              <li>识别自己的价值观和信念</li>
              <li>了解自己的情绪模式</li>
              <li>认识自己的优势和局限</li>
            </ul>
            
            <h3>2. 培养积极的思维模式</h3>
            <p>积极的思维模式能够帮助我们：</p>
            <ul>
              <li>将挫折视为成长的机会</li>
              <li>专注于解决方案而非问题本身</li>
              <li>保持乐观和希望</li>
            </ul>
            
            <h3>3. 建立支持系统</h3>
            <p>没有人是孤岛，建立强大的支持系统包括：</p>
            <ul>
              <li>培养深度的人际关系</li>
              <li>寻找志同道合的伙伴</li>
              <li>学会寻求帮助和支持</li>
            </ul>
            
            <h2>实践建议</h2>
            <p>以下是一些具体的实践方法：</p>
            <ol>
              <li><strong>每日反思</strong>：花10分钟回顾一天的经历和感受</li>
              <li><strong>冥想练习</strong>：通过冥想提高自我觉察能力</li>
              <li><strong>设定小目标</strong>：通过完成小目标建立信心</li>
              <li><strong>学习新技能</strong>：不断学习和成长</li>
              <li><strong>帮助他人</strong>：通过帮助他人获得成就感</li>
            </ol>
            
            <h2>结语</h2>
            <p>建立强大的内心力量是一个持续的过程，需要时间、耐心和坚持。记住，每个人都有无限的潜能，关键是要相信自己，并采取行动。从今天开始，让我们一起踏上这段自我成长的旅程吧！</p>
          </div>
        `,
        summary: '探讨如何通过自我认知、积极思维和支持系统来建立强大的内心力量，提供实用的培养方法和实践建议。',
        author: '心理学博士 李明',
        author_id: 'author-123',
        author_avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=author123',
        category: '心理成长',
        difficulty: 'intermediate',
        reading_time: 8,
        views: 2847,
        likes: 156,
        bookmarks: 89,
        tags: ['内心力量', '自我认知', '心理韧性', '个人成长', '积极心理学'],
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-15T10:30:00Z',
        is_featured: true,
        is_liked: false,
        is_bookmarked: false
      }

      const response = await mockApiResponse(mockArticle)
      const articleData = response.data
      setArticle(articleData)
      setIsLiked(articleData.is_liked)
      setIsBookmarked(articleData.is_bookmarked)
      setLikesCount(articleData.likes)
      setBookmarksCount(articleData.bookmarks)
    } catch (error) {
      console.error('加载文章失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLike = async () => {
    try {
      await mockApiResponse({ success: true })
      setIsLiked(!isLiked)
      setLikesCount(prev => isLiked ? prev - 1 : prev + 1)
    } catch (error) {
      console.error('点赞失败:', error)
    }
  }

  const handleBookmark = async () => {
    try {
      await mockApiResponse({ success: true })
      setIsBookmarked(!isBookmarked)
      setBookmarksCount(prev => isBookmarked ? prev - 1 : prev + 1)
    } catch (error) {
      console.error('收藏失败:', error)
    }
  }

  const getDifficultyLabel = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return '入门'
      case 'intermediate': return '进阶'
      case 'advanced': return '高级'
      default: return '未知'
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800'
      case 'intermediate': return 'bg-yellow-100 text-yellow-800'
      case 'advanced': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载文章中...</p>
        </div>
      </div>
    )
  }

  if (!article) {
    return (
      <div className="text-center py-12">
        <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">文章未找到</h3>
        <p className="text-gray-600 mb-4">请检查链接是否正确</p>
        <Link href="/inspiration">
          <Button>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回精选内容
          </Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* 返回按钮 */}
      <Link href="/inspiration" className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900">
        <ArrowLeft className="mr-2 h-4 w-4" />
        返回精选内容
      </Link>

      {/* 文章头部 */}
      <Card>
        <CardHeader className="border-b">
          <div className="flex items-center gap-2 mb-3">
            <Badge variant="outline">{article.category}</Badge>
            <Badge className={getDifficultyColor(article.difficulty)}>
              {getDifficultyLabel(article.difficulty)}
            </Badge>
            {article.is_featured && (
              <Badge className="bg-purple-100 text-purple-800">
                <Star className="mr-1 h-3 w-3" />
                精选
              </Badge>
            )}
          </div>
          
          <CardTitle className="text-3xl font-bold tracking-tight mb-4">
            {article.title}
          </CardTitle>
          
          <CardDescription className="text-base leading-relaxed mb-4">
            {article.summary}
          </CardDescription>
          
          {/* 作者信息和文章元数据 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={article.author_avatar} />
                <AvatarFallback>
                  <User className="h-5 w-5" />
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium text-gray-900">{article.author}</p>
                <p className="text-sm text-gray-600">{formatDate(article.created_at)}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span className="flex items-center">
                <Clock className="mr-1 h-4 w-4" />
                {article.reading_time} 分钟
              </span>
              <span className="flex items-center">
                <Eye className="mr-1 h-4 w-4" />
                {article.views.toLocaleString()}
              </span>
            </div>
          </div>
        </CardHeader>
        
        {/* 文章内容 */}
        <CardContent className="py-8">
          <div 
            className="prose prose-lg max-w-none dark:prose-invert"
            dangerouslySetInnerHTML={{ __html: article.content }}
          />
        </CardContent>
        
        {/* 文章标签 */}
        <div className="px-6 pb-4">
          <div className="flex flex-wrap gap-2">
            {article.tags.map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                #{tag}
              </Badge>
            ))}
          </div>
        </div>
        
        {/* 互动按钮 */}
        <div className="border-t px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant={isLiked ? "default" : "ghost"}
                size="sm"
                onClick={handleLike}
                className="flex items-center gap-2"
              >
                <Heart className={`h-4 w-4 ${isLiked ? 'fill-current' : ''}`} />
                <span>{likesCount}</span>
              </Button>
              
              <Button
                variant={isBookmarked ? "default" : "ghost"}
                size="sm"
                onClick={handleBookmark}
                className="flex items-center gap-2"
              >
                <Bookmark className={`h-4 w-4 ${isBookmarked ? 'fill-current' : ''}`} />
                <span>{bookmarksCount}</span>
              </Button>
              
              <ShareDialog
                title={article.title}
                content={article.summary}
                articleId={article.id}
                author={article.author}
              />
            </div>
          </div>
        </div>
      </Card>

      {/* 打赏系统 */}
      <RewardSystem
        authorId={article.author_id}
        authorName={article.author}
        postId={article.id}
        postType="inspiration_article"
        postTitle={article.title}
      />

      {/* 评论系统 */}
      <CommentSystem
        postId={article.id}
        postType="inspiration"
        allowAnonymous={true}
      />
    </div>
  )
}
